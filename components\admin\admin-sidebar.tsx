"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Sidebar,
   SidebarContent,
   SidebarFooter,
   SidebarGroup,
   SidebarGroupContent,
   SidebarHeader,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarTrigger,
} from "@/components/ui/sidebar";
import { getCurrentAdmin, logoutAction } from "@/lib/actions/auth-actions";
import { AdminSession } from "@/lib/models";
import { cn } from "@/lib/utils";
import {
   Bars3BottomRightIcon,
   BookmarkSquareIcon,
   HomeIcon,
   PhotoIcon,
   TagIcon,
   UserIcon,
   WrenchScrewdriverIcon,
} from "@heroicons/react/24/solid";
import { LogOut } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const navigationItems = [
   {
      name: "Home",
      href: "/admin",
      icon: HomeIcon,
      description: "Dashboard overview",
   },
   {
      name: "Albums",
      href: "/admin/albums",
      icon: BookmarkSquareIcon,
      description: "Album management",
   },
   {
      name: "Collections",
      href: "/admin/collections",
      icon: TagIcon,
      description: "Collection management",
   },
   {
      name: "Ungrouped",
      href: "/admin/ungrouped",
      icon: PhotoIcon,
      description: "Images not in albums",
   },
   {
      name: "Account",
      href: "/admin/account",
      icon: UserIcon,
      description: "Account settings",
   },
];

function AdminSidebarContent() {
   const pathname = usePathname();
   const [admin, setAdmin] = useState<AdminSession | null>(null);
   const [loggingOut, setLoggingOut] = useState(false);

   // Load admin data
   useEffect(() => {
      async function loadAdmin() {
         try {
            const result = await getCurrentAdmin();
            if (result.success && result.data) {
               setAdmin(result.data as AdminSession);
            }
         } catch (error) {
            console.error("Error loading admin:", error);
         }
      }

      loadAdmin();
   }, []);

   const isActive = (href: string) => {
      if (href === "/admin") {
         return pathname === "/admin";
      }
      return pathname.startsWith(href);
   };

   const handleLogout = async () => {
      setLoggingOut(true);
      try {
         await logoutAction();
         toast.success("Logged out successfully");
      } catch (error) {
         console.error("Error logging out:", error);
         toast.error("Failed to log out");
         setLoggingOut(false);
      }
   };

   return (
      <>
         <SidebarHeader className="p-6 border-b border-border/50">
            <div className="flex items-center space-x-3">
               <div className="flex items-center">
                  {/* Logo */}
                  <Link href="/">
                     <Image
                        src="/astral-logo.svg"
                        alt="Astral logo"
                        width={100}
                        height={40}
                        className="h-10 w-auto"
                     />
                  </Link>
               </div>
            </div>
         </SidebarHeader>

         <SidebarContent className="flex-1 p-2">
            <SidebarGroup>
               <SidebarGroupContent>
                  <SidebarMenu className="space-y-2">
                     {navigationItems.map((item) => {
                        const Icon = item.icon;
                        const active = isActive(item.href);

                        return (
                           <SidebarMenuItem key={item.name}>
                              <SidebarMenuButton
                                 asChild
                                 isActive={active}
                                 className={cn(
                                    "h-auto p-0 hover:bg-transparent",
                                    active && "bg-transparent"
                                 )}
                              >
                                 <Link
                                    href={item.href}
                                    className={cn(
                                       "flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group/item w-full",
                                       active
                                          ? "bg-primary text-primary-foreground shadow-sm"
                                          : "text-muted-foreground hover:text-foreground hover:bg-accent"
                                    )}
                                 >
                                    <Icon
                                       className={cn(
                                          "size-5 shrink-0 transition-colors",
                                          active
                                             ? "text-primary-foreground"
                                             : "text-muted-foreground group-hover/item:text-foreground"
                                       )}
                                    />
                                    <div className="flex-1 min-w-0">
                                       <div
                                          className={cn(
                                             "text-sm font-semibold",
                                             active
                                                ? "text-primary-foreground"
                                                : ""
                                          )}
                                       >
                                          {item.name}
                                       </div>
                                       <div
                                          className={cn(
                                             "text-xs font-semibold truncate transition-colors",
                                             active
                                                ? "text-primary-foreground/80"
                                                : "text-muted-foreground/80 group-hover/item:text-foreground/80"
                                          )}
                                       >
                                          {item.description}
                                       </div>
                                    </div>
                                 </Link>
                              </SidebarMenuButton>
                           </SidebarMenuItem>
                        );
                     })}
                  </SidebarMenu>
               </SidebarGroupContent>
            </SidebarGroup>
         </SidebarContent>

         <SidebarFooter className="p-4 border-t border-border/50 space-y-3">
            {/* Admin Info */}
            <div className="flex items-center space-x-3 px-2">
               <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <WrenchScrewdriverIcon className="w-4 h-4 text-primary" />
               </div>
               <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-foreground">
                     {admin?.name || "Admin User"}
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                     {admin?.email || "<EMAIL>"}
                  </div>
               </div>
            </div>

            {/* Logout Button */}
            <Button
               onClick={handleLogout}
               disabled={loggingOut}
               variant="ghost"
               className="w-full justify-start text-muted-foreground hover:text-foreground"
            >
               {loggingOut ? (
                  <div className="flex items-center gap-2">
                     <div className="animate-spin rounded-full h-3 w-3 border-b border-current"></div>
                     Logging out...
                  </div>
               ) : (
                  <>
                     <LogOut className="w-4 h-4" />
                     Sign Out
                  </>
               )}
            </Button>
         </SidebarFooter>
      </>
   );
}

export function AdminSidebar() {
   return (
      <Sidebar
         variant="sidebar"
         collapsible="offcanvas"
         className="w-64 bg-card border-r border-border/50"
      >
         <AdminSidebarContent />
      </Sidebar>
   );
}

// Mobile trigger component for the top header
export function AdminSidebarMobileTrigger() {
   return (
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-card border-b border-border/50 px-4 py-3">
         <div className="flex items-center justify-between">
            <div className="flex items-center">
               {/* Logo */}
               <Link href="/">
                  <Image
                     src="/astral-logo.svg"
                     alt="Astral logo"
                     width={100}
                     height={40}
                     className="h-10 w-auto"
                  />
               </Link>
            </div>
            <SidebarTrigger className="p-2">
               <Bars3BottomRightIcon className="size-6" />
               <span className="sr-only">Toggle menu</span>
            </SidebarTrigger>
         </div>
      </div>
   );
}
