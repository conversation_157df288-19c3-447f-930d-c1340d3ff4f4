"use client";

import EditCollectionDialog from "@/components/admin/edit-collection-dialog";
import SelectImagesForCollectionDialog from "@/components/admin/select-images-for-collection-dialog";
import AddToCollectionDialog from "@/components/ui/add-to-collection-dialog";
import BulkAddToCollectionDialog from "@/components/ui/bulk-add-to-collection-dialog";
import BulkDeleteImagesDialog from "@/components/ui/bulk-delete-images-dialog";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import CollectionFloatingActionBar from "@/components/ui/collection-floating-action-bar";
import DeleteImageDialog from "@/components/ui/delete-image-dialog";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import MasonryGallery from "@/components/ui/masonry-gallery";
import MoveToAlbumDialog from "@/components/ui/move-to-album-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useCollection } from "@/lib/hooks/use-collections";
import {
   useBulkDeleteImages,
   useBulkUpdateImageCollections,
   useDeleteImageCompletely,
   useInfiniteImages,
   useMoveImageToAlbum,
   useUpdateImageCollections,
} from "@/lib/hooks/use-images";
import { downloadImagesAsZip } from "@/lib/utils/download-utils";
import { TagIcon } from "@heroicons/react/24/solid";
import {
   ArrowLeft,
   Download,
   Edit,
   ImageIcon,
   MoreVertical,
   Plus,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";

export default function CollectionDetailPage() {
   const params = useParams();
   const collectionId = params.id as string;

   const [moveToAlbumDialog, setMoveToAlbumDialog] = useState<{
      open: boolean;
      imageId: string;
   }>({ open: false, imageId: "" });

   const [addToCollectionDialog, setAddToCollectionDialog] = useState<{
      open: boolean;
      imageId: string;
      currentCollectionIds: string[];
   }>({ open: false, imageId: "", currentCollectionIds: [] });

   const [deleteDialog, setDeleteDialog] = useState<{
      open: boolean;
      imageId: string;
      imageName: string;
   }>({ open: false, imageId: "", imageName: "" });

   const [dropdownOpen, setDropdownOpen] = useState(false);
   const [editDialogOpen, setEditDialogOpen] = useState(false);
   const [selectImagesDialogOpen, setSelectImagesDialogOpen] = useState(false);
   const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());

   // Bulk action dialogs
   const [bulkAddToCollectionDialog, setBulkAddToCollectionDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [bulkDeleteDialog, setBulkDeleteDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [isDownloading, setIsDownloading] = useState(false);

   const {
      data: collection,
      isLoading: collectionLoading,
      error: collectionError,
   } = useCollection(collectionId);
   const {
      data: imagesData,
      isLoading: imagesLoading,
      error: imagesError,
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
   } = useInfiniteImages({ collectionId, limit: 20 });

   // Flatten all images from all pages
   const images = useMemo(() => {
      if (!imagesData?.pages) return [];
      return imagesData.pages.flatMap((page) => page.data);
   }, [imagesData?.pages]);

   const totalImages = useMemo(() => {
      if (!imagesData?.pages || imagesData.pages.length === 0) return 0;
      return imagesData.pages[0].pagination.total;
   }, [imagesData?.pages]);

   // Mutations
   const deleteImageMutation = useDeleteImageCompletely();
   const moveImageMutation = useMoveImageToAlbum();
   const updateCollectionsMutation = useUpdateImageCollections();
   const bulkUpdateCollectionsMutation = useBulkUpdateImageCollections();
   const bulkDeleteImagesMutation = useBulkDeleteImages();

   if (collectionError || imagesError) {
      return (
         <div className="p-8">
            <div className="text-center text-destructive">
               <p>Failed to load collection. Please try again.</p>
            </div>
         </div>
      );
   }

   if (collectionLoading) {
      return (
         <div className="p-8 space-y-8">
            {/* Header Skeleton */}
            <div className="flex items-center justify-between">
               <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10" />
                  <div className="space-y-2">
                     <Skeleton className="h-8 w-48" />
                     <Skeleton className="h-4 w-32" />
                  </div>
               </div>
               <div className="flex space-x-3">
                  <Skeleton className="h-10 w-20" />
                  <Skeleton className="h-10 w-32" />
               </div>
            </div>

            {/* Collection Info Skeleton */}
            <Card className="border-border/50 py-0">
               <CardContent className="p-6">
                  <div className="flex items-start space-x-6">
                     <Skeleton className="w-16 h-16 rounded-full" />
                     <div className="flex-1 space-y-3">
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                     </div>
                  </div>
               </CardContent>
            </Card>
         </div>
      );
   }

   if (!collection) {
      return (
         <div className="p-8">
            <div className="text-center">
               <p>Collection not found.</p>
            </div>
         </div>
      );
   }

   const hasImages = images.length > 0;

   const handleImageSelect = (imageId: string, selected: boolean) => {
      const newSelected = new Set(selectedImages);
      if (selected) {
         newSelected.add(imageId);
      } else {
         newSelected.delete(imageId);
      }
      setSelectedImages(newSelected);
   };

   const handleClearSelection = () => {
      setSelectedImages(new Set());
   };

   const handleBulkAddToCollection = () => {
      setBulkAddToCollectionDialog({ open: true });
   };

   const handleBulkDelete = () => {
      setBulkDeleteDialog({ open: true });
   };

   // Bulk action handlers
   const handleBulkAddToCollections = async (
      imageIds: string[],
      collectionIds: string[]
   ) => {
      await bulkUpdateCollectionsMutation.mutateAsync({
         imageIds,
         collectionIds,
      });
      setSelectedImages(new Set());
   };

   const handleBulkDeleteImages = async (): Promise<void> => {
      const imageIds = Array.from(selectedImages);
      await bulkDeleteImagesMutation.mutateAsync(imageIds);
      setSelectedImages(new Set());
   };

   const handleSelectAll = () => {
      if (selectedImages.size === images.length) {
         setSelectedImages(new Set());
      } else {
         setSelectedImages(new Set(images.map((img) => img._id!)));
      }
   };

   const handleDownloadAll = async () => {
      if (images.length === 0) return;

      try {
         setIsDownloading(true);
         const imagesForDownload = images.map((img) => ({
            url: img.url,
            name: img.name || `image_${img._id}`,
         }));

         await downloadImagesAsZip(
            imagesForDownload,
            `${collection?.name || "collection"}_images.zip`
         );
      } catch (error) {
         console.error("Error downloading images:", error);
         // You might want to show a toast notification here
      } finally {
         setIsDownloading(false);
      }
   };

   // Handle image actions
   const handleImageAction = (imageId: string, action: string) => {
      const image = images.find((img) => img._id === imageId);
      if (!image) return;

      switch (action) {
         case "move-to-album":
            setMoveToAlbumDialog({
               open: true,
               imageId,
            });
            break;
         case "add-to-collection":
            setAddToCollectionDialog({
               open: true,
               imageId,
               currentCollectionIds: image.collectionIds || [],
            });
            break;
         case "delete":
            setDeleteDialog({
               open: true,
               imageId,
               imageName: image.name,
            });
            break;
         default:
            console.log("Unknown action:", action);
      }
   };

   // Handle move to album
   const handleMoveToAlbum = async (
      imageId: string,
      targetAlbumId: string
   ): Promise<void> => {
      await moveImageMutation.mutateAsync({ imageId, albumId: targetAlbumId });
   };

   // Handle add to collections
   const handleAddToCollections = async (
      imageId: string,
      collectionIds: string[]
   ): Promise<void> => {
      await updateCollectionsMutation.mutateAsync({ imageId, collectionIds });
   };

   // Handle delete image
   const handleDeleteImage = async (): Promise<void> => {
      if (deleteDialog.imageId) {
         await deleteImageMutation.mutateAsync(deleteDialog.imageId);
      }
   };

   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-3xl font-bold text-foreground">
                     {collection.name}
                  </h1>
                  <p className="text-muted-foreground">
                     {collection.imageCount} images
                  </p>
               </div>
            </div>
            {/* Desktop actions */}
            <div className="hidden sm:flex items-center space-x-3">
               <Link href="/admin/collections">
                  <Button variant="outline">
                     <ArrowLeft className="w-4 h-4" />
                     Back to Collections
                  </Button>
               </Link>
               <Button
                  variant="outline"
                  onClick={() => setEditDialogOpen(true)}
               >
                  <Edit className="w-4 h-4 " />
                  Edit Collection
               </Button>
               {hasImages && (
                  <Button
                     variant="outline"
                     onClick={handleDownloadAll}
                     disabled={isDownloading}
                  >
                     <Download className="w-4 h-4" />
                     {isDownloading ? "Downloading..." : "Download All"}
                  </Button>
               )}
               <Button
                  className="bg-gradient-accent hover:opacity-90"
                  onClick={() => setSelectImagesDialogOpen(true)}
               >
                  <Plus className="w-4 h-4 " />
                  Add Images
               </Button>
            </div>
            {/* Mobile actions dropdown */}
            <div className="flex sm:hidden items-center">
               <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                  <DropdownMenuTrigger asChild>
                     <Button variant="outline" size="icon">
                        <MoreVertical className="w-5 h-5" />
                        <span className="sr-only">Open actions</span>
                     </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="space-y-2">
                     <DropdownMenuItem
                        asChild
                        onClick={() => setDropdownOpen(false)}
                     >
                        <Link href="/admin/collections">
                           <span className="flex items-center">
                              <ArrowLeft className="w-4 h-4 mr-2" /> Back to
                              Collections
                           </span>
                        </Link>
                     </DropdownMenuItem>
                     <DropdownMenuItem
                        onClick={() => {
                           setDropdownOpen(false);
                           setEditDialogOpen(true);
                        }}
                     >
                        <Edit className="w-4 h-4 mr-2" /> Edit Collection
                     </DropdownMenuItem>
                     {hasImages && (
                        <DropdownMenuItem
                           onClick={() => {
                              setDropdownOpen(false);
                              handleDownloadAll();
                           }}
                           disabled={isDownloading}
                        >
                           <Download className="w-4 h-4 mr-2" />
                           {isDownloading ? "Downloading..." : "Download All"}
                        </DropdownMenuItem>
                     )}
                     <DropdownMenuItem
                        onClick={() => {
                           setDropdownOpen(false);
                           setSelectImagesDialogOpen(true);
                        }}
                     >
                        <Plus className="w-4 h-4 mr-2" /> Add Images
                     </DropdownMenuItem>
                  </DropdownMenuContent>
               </DropdownMenu>
            </div>
         </div>

         {/* Collection Info */}
         <Card className="border-border/50 py-0">
            <CardContent className="p-6">
               <div className="flex items-start space-x-6">
                  {/* Collection Icon */}
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                     <TagIcon className="w-8 h-8 text-primary" />
                  </div>

                  {/* Collection Details */}
                  <div className="flex-1">
                     <h2 className="text-xl font-semibold text-foreground mb-2">
                        {collection.name}
                     </h2>
                     {collection.description && (
                        <p className="text-muted-foreground mb-4">
                           {collection.description}
                        </p>
                     )}
                     <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>{collection.imageCount} images</span>
                        <span>•</span>
                        <span>
                           Created{" "}
                           {new Date(collection.createdAt).toLocaleDateString()}
                        </span>
                     </div>
                  </div>
               </div>
            </CardContent>
         </Card>

         {/* Images Gallery */}
         <Card className="border-border/50">
            <CardHeader>
               <div className="flex items-center justify-between">
                  <div>
                     <CardTitle className="text-foreground">
                        Images in Collection
                     </CardTitle>
                     <CardDescription>
                        {hasImages
                           ? `${images.length} of ${totalImages} images tagged with this collection`
                           : `All images tagged with this collection`}
                     </CardDescription>
                  </div>

                  {hasImages && (
                     <Button variant="outline" onClick={handleSelectAll}>
                        {selectedImages.size === images.length
                           ? "Deselect All"
                           : "Select All"}
                     </Button>
                  )}
               </div>
            </CardHeader>
            <CardContent>
               {imagesLoading ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                     {Array.from({ length: 12 }).map((_, i) => (
                        <Skeleton
                           key={i}
                           className="aspect-square rounded-lg"
                        />
                     ))}
                  </div>
               ) : hasImages ? (
                  <MasonryGallery
                     images={images}
                     selectedImages={selectedImages}
                     onImageSelect={handleImageSelect}
                     onImageAction={handleImageAction}
                     showSetCover={false}
                     showMoveToAlbum={true}
                     showAddToCollection={true}
                     showCheckboxes={true}
                     hasNextPage={hasNextPage}
                     isFetchingNextPage={isFetchingNextPage}
                     fetchNextPage={fetchNextPage}
                     isLoading={imagesLoading}
                  />
               ) : (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                     <ImageIcon className="w-16 h-16 mb-4 opacity-50" />
                     <h3 className="text-lg font-medium mb-2">
                        No images in collection
                     </h3>
                     <p className="text-sm mb-4 text-center">
                        Add images from your gallery to this collection
                     </p>
                     <Button
                        className="bg-gradient-accent hover:opacity-90"
                        onClick={() => setSelectImagesDialogOpen(true)}
                     >
                        <Plus className="w-4 h-4 " />
                        Add Images
                     </Button>
                  </div>
               )}
            </CardContent>
         </Card>

         {/* Floating Action Bar */}
         <CollectionFloatingActionBar
            selectedCount={selectedImages.size}
            onClearSelection={handleClearSelection}
            onAddToCollection={handleBulkAddToCollection}
            onDelete={handleBulkDelete}
         />

         {/* Dialogs */}
         <MoveToAlbumDialog
            open={moveToAlbumDialog.open}
            onOpenChange={(open) => setMoveToAlbumDialog({ open, imageId: "" })}
            imageId={moveToAlbumDialog.imageId}
            currentAlbumId={null}
            onMove={handleMoveToAlbum}
         />

         <AddToCollectionDialog
            open={addToCollectionDialog.open}
            onOpenChange={(open) =>
               setAddToCollectionDialog({
                  open,
                  imageId: "",
                  currentCollectionIds: [],
               })
            }
            imageId={addToCollectionDialog.imageId}
            currentCollectionIds={addToCollectionDialog.currentCollectionIds}
            onAddToCollections={handleAddToCollections}
         />

         <DeleteImageDialog
            open={deleteDialog.open}
            onOpenChange={(open) =>
               setDeleteDialog({ open, imageId: "", imageName: "" })
            }
            imageName={deleteDialog.imageName}
            onConfirm={handleDeleteImage}
            isDeleting={deleteImageMutation.isPending}
         />
         <EditCollectionDialog
            open={editDialogOpen}
            onOpenChange={setEditDialogOpen}
            collection={collection}
         />
         <SelectImagesForCollectionDialog
            open={selectImagesDialogOpen}
            onOpenChange={setSelectImagesDialogOpen}
            collection={collection}
            onSuccess={() => {
               // Optionally, you can refetch images here if needed
            }}
         />

         {/* Bulk Action Dialogs */}
         <BulkAddToCollectionDialog
            open={bulkAddToCollectionDialog.open}
            onOpenChange={(open) => setBulkAddToCollectionDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onAddToCollections={handleBulkAddToCollections}
         />

         <BulkDeleteImagesDialog
            open={bulkDeleteDialog.open}
            onOpenChange={(open) => setBulkDeleteDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onConfirm={handleBulkDeleteImages}
            isDeleting={bulkDeleteImagesMutation.isPending}
         />
      </div>
   );
}
