"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   getCurrentAdmin,
   updateAdminAccount,
} from "@/lib/actions/auth-actions";
import { AdminSession } from "@/lib/models";
import {
   EnvelopeIcon,
   LockClosedIcon,
   UserIcon,
} from "@heroicons/react/24/solid";
import { Download, Eye, EyeOff } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function AccountPage() {
   const [loading, setLoading] = useState(true);
   const [saving, setSaving] = useState(false);

   // Form states
   const [name, setName] = useState("");
   const [email, setEmail] = useState("");
   const [newPassword, setNewPassword] = useState("");
   const [confirmPassword, setConfirmPassword] = useState("");

   // Password visibility states
   const [showNewPassword, setShowNewPassword] = useState(false);
   const [showConfirmPassword, setShowConfirmPassword] = useState(false);

   // Load current admin data
   useEffect(() => {
      async function loadAdmin() {
         try {
            const result = await getCurrentAdmin();
            if (result.success && result.data) {
               const adminData = result.data as AdminSession;
               setName(adminData.name);
               setEmail(adminData.email);
            } else {
               toast.error("Failed to load account information");
            }
         } catch (error) {
            console.error("Error loading admin:", error);
            toast.error("Failed to load account information");
         } finally {
            setLoading(false);
         }
      }

      loadAdmin();
   }, []);

   const handleUpdateProfile = async (e: React.FormEvent) => {
      e.preventDefault();
      setSaving(true);

      try {
         const result = await updateAdminAccount({
            name: name.trim(),
            email: email.trim(),
         });

         if (result.success) {
            toast.success("Profile updated successfully");
         } else {
            toast.error(result.error || "Failed to update profile");
         }
      } catch (error) {
         console.error("Error updating profile:", error);
         toast.error("Failed to update profile");
      } finally {
         setSaving(false);
      }
   };

   const handleUpdatePassword = async (e: React.FormEvent) => {
      e.preventDefault();
      setSaving(true);

      try {
         if (newPassword !== confirmPassword) {
            toast.error("New passwords do not match");
            return;
         }

         if (newPassword.length < 6) {
            toast.error("Password must be at least 6 characters long");
            return;
         }

         const result = await updateAdminAccount({
            password: newPassword,
         });

         if (result.success) {
            toast.success("Password updated successfully");
            setNewPassword("");
            setConfirmPassword("");
         } else {
            toast.error(result.error || "Failed to update password");
         }
      } catch (error) {
         console.error("Error updating password:", error);
         toast.error("Failed to update password");
      } finally {
         setSaving(false);
      }
   };

   if (loading) {
      return (
         <div className="p-8">
            <div className="flex items-center justify-center h-64">
               <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
         </div>
      );
   }

   return (
      // <div className="p-8 space-y-8 max-w-2xl">
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="space-y-2">
            <h1 className="text-3xl font-bold text-foreground">
               Account Settings
            </h1>
            <p className="text-muted-foreground">
               Manage your admin account information and security settings
            </p>
         </div>

         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Profile Information */}
            <Card className="border-border/50">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-foreground text-xl">
                     <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-primary/20 text-primary">
                        <UserIcon className="h-5 w-5" />
                     </span>
                     Profile Information
                  </CardTitle>
                  <CardDescription>
                     Update your name and email address
                  </CardDescription>
               </CardHeader>
               <CardContent>
                  <form onSubmit={handleUpdateProfile} className="space-y-4">
                     <div className="space-y-4">
                        <Label htmlFor="name" className="text-foreground">
                           <UserIcon className="h-4 w-4" />
                           Name
                        </Label>
                        <Input
                           id="name"
                           type="text"
                           value={name}
                           onChange={(e) => setName(e.target.value)}
                           placeholder="Enter your name"
                           required
                           disabled={saving}
                        />
                     </div>
                     <div className="space-y-4">
                        <Label htmlFor="email" className="text-foreground">
                           <EnvelopeIcon className="h-4 w-4" />
                           Email Address
                        </Label>
                        <Input
                           id="email"
                           type="email"
                           value={email}
                           onChange={(e) => setEmail(e.target.value)}
                           placeholder="Enter your email"
                           required
                           disabled={saving}
                        />
                     </div>
                     <Button
                        type="submit"
                        disabled={saving || !name.trim() || !email.trim()}
                        className="w-full sm:w-auto"
                     >
                        {saving ? (
                           <div className="flex items-center gap-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                              Saving...
                           </div>
                        ) : (
                           <>
                              <Download className="size-4" />
                              Save Profile
                           </>
                        )}
                     </Button>
                  </form>
               </CardContent>
            </Card>
            {/* Password Security */}
            <Card className="border-border/50">
               <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-foreground text-xl">
                     <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-primary/20 text-primary">
                        <LockClosedIcon className="h-5 w-5" />
                     </span>
                     Password Security
                  </CardTitle>
                  <CardDescription>
                     Update your password to keep your account secure
                  </CardDescription>
               </CardHeader>
               <CardContent>
                  <form onSubmit={handleUpdatePassword} className="space-y-4">
                     <div className="space-y-4">
                        <Label
                           htmlFor="newPassword"
                           className="text-foreground"
                        >
                           <LockClosedIcon className="h-4 w-4" />
                           New Password
                        </Label>
                        <div className="relative">
                           <Input
                              id="newPassword"
                              type={showNewPassword ? "text" : "password"}
                              value={newPassword}
                              onChange={(e) => setNewPassword(e.target.value)}
                              placeholder="Enter new password"
                              required
                              className="pr-10"
                              disabled={saving}
                           />
                           <button
                              type="button"
                              onClick={() =>
                                 setShowNewPassword(!showNewPassword)
                              }
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                              disabled={saving}
                           >
                              {showNewPassword ? (
                                 <EyeOff className="h-4 w-4" />
                              ) : (
                                 <Eye className="h-4 w-4" />
                              )}
                           </button>
                        </div>
                     </div>
                     <div className="space-y-4">
                        <Label
                           htmlFor="confirmPassword"
                           className="text-foreground"
                        >
                           <LockClosedIcon className="h-4 w-4" />
                           Confirm New Password
                        </Label>
                        <div className="relative">
                           <Input
                              id="confirmPassword"
                              type={showConfirmPassword ? "text" : "password"}
                              value={confirmPassword}
                              onChange={(e) =>
                                 setConfirmPassword(e.target.value)
                              }
                              placeholder="Confirm new password"
                              required
                              className="pr-10"
                              disabled={saving}
                           />
                           <button
                              type="button"
                              onClick={() =>
                                 setShowConfirmPassword(!showConfirmPassword)
                              }
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                              disabled={saving}
                           >
                              {showConfirmPassword ? (
                                 <EyeOff className="h-4 w-4" />
                              ) : (
                                 <Eye className="h-4 w-4" />
                              )}
                           </button>
                        </div>
                     </div>
                     <Button
                        type="submit"
                        disabled={saving || !newPassword || !confirmPassword}
                        className="w-full sm:w-auto"
                     >
                        {saving ? (
                           <div className="flex items-center gap-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                              Updating...
                           </div>
                        ) : (
                           <>
                              <LockClosedIcon className="h-4 w-4" />
                              Update Password
                           </>
                        )}
                     </Button>
                  </form>
               </CardContent>
            </Card>
         </div>
      </div>
   );
}
