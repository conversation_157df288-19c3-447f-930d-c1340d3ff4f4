"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Image as ImageType } from "@/lib/models";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from "./dialog";
import ImageActionMenu from "./image-action-menu";

interface MasonryGalleryProps {
   images: ImageType[];
   selectedImages?: Set<string>;
   onImageSelect?: (imageId: string, selected: boolean) => void;
   onImageAction?: (imageId: string, action: string) => void;
   showActions?: boolean;
   showSetCover?: boolean;
   showMoveToAlbum?: boolean;
   showAddToCollection?: boolean;
   showCheckboxes?: boolean;
   className?: string;
   // Infinite scroll props
   hasNextPage?: boolean;
   isFetchingNextPage?: boolean;
   fetchNextPage?: () => void;
   isLoading?: boolean;
}

interface ImageModalProps {
   image: ImageType | null;
   isOpen: boolean;
   onClose: () => void;
}

function ImageModal({ image, isOpen, onClose }: ImageModalProps) {
   if (!image) return null;

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className="max-w-4xl max-h-[90vh] p-0">
            <DialogHeader className="p-6 pb-0">
               <DialogTitle>{image.name}</DialogTitle>
               <DialogDescription>
                  {image.width} × {image.height} •{" "}
                  {(image.fileSize / 1024 / 1024).toFixed(2)} MB
               </DialogDescription>
            </DialogHeader>
            <div className="relative w-full h-[70vh] bg-muted">
               <Image
                  src={image.url}
                  alt={image.name}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
               />
            </div>
         </DialogContent>
      </Dialog>
   );
}

export default function MasonryGallery({
   images,
   selectedImages = new Set(),
   onImageSelect,
   onImageAction,
   showActions = true,
   showSetCover = false,
   showMoveToAlbum = true,
   showAddToCollection = true,
   showCheckboxes = false,
   className,
   // Infinite scroll props
   hasNextPage = false,
   isFetchingNextPage = false,
   fetchNextPage,
   isLoading = false,
}: MasonryGalleryProps) {
   const [selectedImage, setSelectedImage] = useState<ImageType | null>(null);
   const [isModalOpen, setIsModalOpen] = useState(false);
   const observerRef = useRef<IntersectionObserver | null>(null);
   const loadingRef = useRef<HTMLDivElement>(null);

   const handleImageClick = (image: ImageType) => {
      setSelectedImage(image);
      setIsModalOpen(true);
   };

   const handleCloseModal = () => {
      setIsModalOpen(false);
      setSelectedImage(null);
   };

   const handleCheckboxChange = (imageId: string, checked: boolean) => {
      onImageSelect?.(imageId, checked);
   };

   // Infinite scroll observer
   const handleObserver = useCallback(
      (entries: IntersectionObserverEntry[]) => {
         const [target] = entries;
         if (
            target.isIntersecting &&
            hasNextPage &&
            !isFetchingNextPage &&
            fetchNextPage
         ) {
            fetchNextPage();
         }
      },
      [hasNextPage, isFetchingNextPage, fetchNextPage]
   );

   useEffect(() => {
      const element = loadingRef.current;
      if (!element) return;

      observerRef.current = new IntersectionObserver(handleObserver, {
         rootMargin: "100px", // Start loading 100px before reaching the bottom
      });

      observerRef.current.observe(element);

      return () => {
         if (observerRef.current) {
            observerRef.current.disconnect();
         }
      };
   }, [handleObserver]);

   if (isLoading) {
      return (
         <div
            className={cn("flex items-center justify-center h-64", className)}
         >
            <div className="flex items-center space-x-2">
               <Loader2 className="h-6 w-6 animate-spin" />
               <span className="text-muted-foreground">Loading images...</span>
            </div>
         </div>
      );
   }

   if (images.length === 0) {
      return (
         <div
            className={cn(
               "flex items-center justify-center h-64 text-muted-foreground",
               className
            )}
         >
            <div className="text-center">
               <p>No images to display</p>
            </div>
         </div>
      );
   }

   return (
      <>
         <div className="space-y-4">
            <div
               className={cn(
                  "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",
                  className
               )}
               style={{
                  gridTemplateRows: "repeat(auto-fill, minmax(200px, auto))",
               }}
            >
               {images.map((image) => (
                  <div
                     key={image._id}
                     className="group relative cursor-pointer"
                     style={{
                        gridRow: `span ${Math.ceil(
                           (image.height / image.width) * 2
                        )}`,
                     }}
                     onClick={() => handleImageClick(image)}
                  >
                     <div className="relative overflow-hidden rounded-lg bg-astral-grey/90 h-full">
                        <Image
                           src={image.url}
                           alt={image.name}
                           width={image.width}
                           height={image.height}
                           className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                           sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                        />

                        {/* Overlay */}
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />

                        {/* Checkbox - Top Left */}
                        {showCheckboxes && (
                           <div
                              className={cn(
                                 "absolute top-2 left-2 z-10 transition-opacity duration-200",
                                 // On mobile: always visible
                                 // On desktop: hidden by default, visible on hover or when selected
                                 selectedImages.size > 0
                                    ? "opacity-100"
                                    : "opacity-100 md:opacity-0 md:group-hover:opacity-100"
                              )}
                              onClick={(e) => e.stopPropagation()}
                           >
                              <Checkbox
                                 checked={selectedImages.has(image._id!)}
                                 onCheckedChange={(checked) =>
                                    handleCheckboxChange(
                                       image._id!,
                                       checked as boolean
                                    )
                                 }
                                 className="bg-background/80 size-5 rounded-sm backdrop-blur-sm border-2"
                              />
                           </div>
                        )}

                        {/* Actions Menu */}
                        {showActions && (
                           <div
                              className="absolute top-2 right-2 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                              onClick={(e) => e.stopPropagation()}
                           >
                              <ImageActionMenu
                                 image={image}
                                 onAction={(action, imageId) =>
                                    onImageAction?.(imageId, action)
                                 }
                                 showSetCover={showSetCover}
                                 showMoveToAlbum={showMoveToAlbum}
                                 showAddToCollection={showAddToCollection}
                                 className="bg-background/80 backdrop-blur-sm"
                              />
                           </div>
                        )}

                        {/* Mobile Actions - Always visible on mobile */}
                        {showActions && (
                           <div
                              className="absolute top-2 right-2 opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity duration-200 sm:hidden"
                              onClick={(e) => e.stopPropagation()}
                           >
                              <ImageActionMenu
                                 image={image}
                                 onAction={(action, imageId) =>
                                    onImageAction?.(imageId, action)
                                 }
                                 showSetCover={showSetCover}
                                 showMoveToAlbum={showMoveToAlbum}
                                 showAddToCollection={showAddToCollection}
                                 className="bg-background/80 backdrop-blur-sm"
                              />
                           </div>
                        )}
                     </div>
                  </div>
               ))}
            </div>

            {/* Loading indicator for infinite scroll */}
            {(hasNextPage || isFetchingNextPage) && (
               <div
                  ref={loadingRef}
                  className="flex items-center justify-center py-8"
               >
                  <div className="flex items-center space-x-2">
                     <Loader2 className="h-5 w-5 animate-spin" />
                     <span className="text-sm text-muted-foreground">
                        {isFetchingNextPage
                           ? "Loading more images..."
                           : "Scroll to load more"}
                     </span>
                  </div>
               </div>
            )}

            {/* End of content indicator */}
            {!hasNextPage && images.length > 0 && (
               <div className="flex items-center justify-center pt-8">
                  <span className="text-sm text-muted-foreground">
                     You&apos;ve reached the end of the gallery
                  </span>
               </div>
            )}
         </div>

         {/* Image Modal */}
         <ImageModal
            image={selectedImage}
            isOpen={isModalOpen}
            onClose={handleCloseModal}
         />
      </>
   );
}
