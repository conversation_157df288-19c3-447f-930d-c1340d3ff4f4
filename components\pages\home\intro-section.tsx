import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function IntroSection() {
   return (
      <>
         <section className="py-20 bg-astral-grey overflow-hidden">
            <div className="container max-w-[1400px] mx-auto py-3 px-6 lg:px-8">
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div className="space-y-6 text-center sm:text-start">
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground">
                        Capturing Stories,
                        <br />
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Creating Magic
                        </span>
                     </h2>
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        At Astral Studios, we believe every moment tells a story
                        worth preserving. Our passion for photography and
                        videography drives us to capture not just images, but
                        emotions, connections, and the authentic beauty of
                        life&apos;s most precious celebrations.
                     </p>
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        With years of experience and a commitment to excellence,
                        we specialize in creating stunning visual narratives
                        that you&apos;ll treasure for a lifetime. From intimate
                        ceremonies to grand celebrations, we&apos;re here to
                        make your special moments unforgettable.
                     </p>
                     <div className="flex flex-col items-center sm:flex-row gap-4">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold py-3 !px-6 h-auto"
                        >
                           <Link href="/about">
                              Learn More About Us{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant={"outline"}
                           size="lg"
                           className="hover:opacity-90 font-montserrat font-semibold py-[14px] !px-6 h-auto"
                        >
                           <Link href="/about">
                              View Portfolio{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                     </div>
                  </div>
                  <div className="relative h-120 w-full">
                     <Image
                        src={"/images/img-1.JPG"}
                        alt="Astral Studios Interior"
                        fill
                        className="object-cover object-center
 rounded-3xl shadow-elegant brightness-90"
                     />
                     {/* Decorative elements */}
                     <div className="absolute -top-6 -left-6 w-24 h-24 bg-gradient-accent rounded-full opacity-40 blur-xl"></div>
                     <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-gradient-accent rounded-full opacity-40 blur-xl"></div>

                     {/* <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-gradient-accent rounded-full opacity-20 animate-glow z-10"></div> */}
                  </div>
               </div>
            </div>
         </section>
      </>
   );
}
