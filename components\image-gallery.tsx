"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Eye, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ImageGalleryProps {
   images: {
      src: string;
      alt: string;
      category?: string;
      width?: number;
      height?: number;
   }[];
   showCategory?: boolean;
   className?: string;
}

export function ImageGallery({
   images,
   showCategory = false,
   className = "",
}: ImageGalleryProps) {
   const [selectedImage, setSelectedImage] = useState<number | null>(null);

   const openLightbox = (index: number) => {
      setSelectedImage(index);
   };

   const closeLightbox = () => {
      setSelectedImage(null);
   };

   const nextImage = () => {
      if (selectedImage !== null) {
         setSelectedImage((selectedImage + 1) % images.length);
      }
   };

   const prevImage = () => {
      if (selectedImage !== null) {
         setSelectedImage(
            selectedImage === 0 ? images.length - 1 : selectedImage - 1
         );
      }
   };

   return (
      <>
         {/* Gallery Grid */}
         <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${className}`}
         >
            {images.map((image, index) => (
               <div
                  key={index}
                  className="group relative overflow-hidden rounded-2xl bg-card border border-astral-grey-light hover:shadow-elegant transition-all duration-500 cursor-pointer"
                  onClick={() => openLightbox(index)}
               >
                  <div className="relative h-85 overflow-hidden">
                     <Image
                        src={image.src}
                        alt={image.alt}
                        fill
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                     />
                     <div className="hidden sm:flex absolute inset-0 items-center justify-center  transition-all duration-300">
                        <div className="w-16 h-16 bg-black/30 opacity-0 group-hover:opacity-100 backdrop-blur-sm rounded-full flex items-center justify-center">
                           <Eye className="h-8 w-8 text-white" />
                        </div>
                     </div>
                     {/* <div className="absolute inset-0 bg-gradient-overlay opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
                        <div className="text-center text-white p-6 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                           <Eye className="h-8 w-8 mx-auto mb-3 opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-200" />
                        </div>
                     </div> */}
                  </div>
                  {showCategory && image.category && (
                     <div className="p-6">
                        <div className="flex items-center justify-between">
                           <span className="text-sm font-montserrat font-medium text-primary bg-primary/10 px-3 py-1 rounded-full">
                              {image.category}
                           </span>
                        </div>
                     </div>
                  )}
               </div>
               // <div
               //    key={index}
               //    className="relative h-80 overflow-hidden rounded-md cursor-pointer group"
               //    onClick={() => openLightbox(index)}
               // >
               //    <Image
               //       src={image.src}
               //       alt={image.alt}
               //       fill
               //       className="object-cover transition-transform duration-300 group-hover:scale-105"
               //       sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
               //    />
               //    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
               // </div>
            ))}
         </div>

         {/* Lightbox */}
         {selectedImage !== null && (
            <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
               <div
                  className="fixed inset-0 -z-50 bg-black/90"
                  onClick={closeLightbox}
               ></div>
               <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-4 right-4 bg-white/20 rounded-full sm:bg-transparent text-white size-12 hover:bg-white/20 z-100"
                  onClick={closeLightbox}
               >
                  <X className="!size:6 sm:!size-8" />
               </Button>

               <Button
                  variant="ghost"
                  size="icon"
                  className="absolute bg-gradient-accent rounded-full left-8 top-1/2 -translate-y-1/2 text-white p-6 hover:bg-white/20 z-100"
                  onClick={prevImage}
               >
                  <ChevronLeft className="size-10 flex" />
               </Button>

               <Button
                  variant="ghost"
                  size="icon"
                  className="absolute bg-gradient-accent rounded-full right-8 top-1/2 -translate-y-1/2 text-white p-6 hover:bg-white/20 z-100"
                  onClick={nextImage}
               >
                  <ChevronRight className="size-10 flex" />
               </Button>

               <div className="relative max-w-4xl w-[800px] h-[800px] max-h-full">
                  <Image
                     src={images[selectedImage].src}
                     alt={images[selectedImage].alt}
                     fill
                     className="object-contain"
                  />
               </div>

               <div className="absolute bottom-6 left-1/2 -translate-x-1/2 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full text-white text-sm">
                  {selectedImage + 1} / {images.length}
               </div>
            </div>
         )}
      </>
   );
}
