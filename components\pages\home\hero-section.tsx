import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export function HeroSection() {
   return (
      <section
         className={`relative min-h-screen flex items-center justify-center`}
      >
         {/* Background Image */}
         <div className="absolute inset-0 z-0">
            <Image
               src={"/images/pre-wedding-shoots/pre-wedding-shoot-2.JPG"}
               alt="Hero background"
               fill
               className="object-cover object-right sm:object-center"
               priority
               sizes="100vw"
            />
            {/* <div className="absolute inset-0 bg-black/60 sm:bg-gradient-overlay"></div> */}
            <div className="absolute inset-0 bg-black/75"></div>
         </div>

         {/* Content */}
         <div className="relative z-10 text-center text-white max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-bold mb-4 leading-tight">
               Astral Studios
            </h1>

            <p className="text-lg sm:text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed text-gray-200">
               Where moments become timeless memories. Professional photography
               and videography services capturing the essence of your most
               precious celebrations.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
               <Button
                  asChild
                  size="lg"
                  className="text-base sm:text-lg !px-6 sm:px-8 w-fit sm:w-auto bg-gradient-accent"
               >
                  <Link href={"/portfolio"}>
                     View Portfolio <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
               </Button>

               <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-white/30 text-white hover:bg-white/10 font-montserrat text-lg px-8"
               >
                  <Link href={"/contact"}>Get In Touch</Link>
               </Button>
            </div>
         </div>
      </section>
   );
}
