"use client";

import { useUploadImage } from "@/lib/hooks/use-images";
import {
   extractImageMetadata,
   validateImageFile,
} from "@/lib/utils/image-processing";
import {
   useCallback,
   useRef,
   useState,
   type ChangeEvent,
   type InputHTMLAttributes,
} from "react";

export type UploadStatus =
   | "pending"
   | "uploading"
   | "success"
   | "error"
   | "cancelled";

export interface FileUploadItem {
   id: string;
   file: File;
   preview: string;
   status: UploadStatus;
   progress: number;
   error?: string;
   metadata?: {
      width: number;
      height: number;
      size: string;
      aspectRatio: string;
   };
}

export interface GalleryUploadOptions {
   maxFiles?: number;
   maxSizeBytes?: number;
   albumId?: string | null;
   collectionIds?: string[];
   concurrentUploads?: number;
}

export interface GalleryUploadState {
   files: FileUploadItem[];
   isUploading: boolean;
   uploadQueue: string[];
   activeUploads: Set<string>;
   errors: string[];
   isDragging: boolean;
}

export interface GalleryUploadActions {
   addFiles: (files: FileList | File[]) => Promise<void>;
   removeFile: (id: string) => void;
   clearFiles: () => void;
   uploadFile: (id: string) => Promise<void>;
   uploadAll: () => Promise<void>;
   cancelUpload: (id: string) => void;
   cancelAllUploads: () => void;
   retryUpload: (id: string) => Promise<void>;
   handleDragEnter: (e: React.DragEvent) => void;
   handleDragLeave: (e: React.DragEvent) => void;
   handleDragOver: (e: React.DragEvent) => void;
   handleDrop: (e: React.DragEvent) => void;
   openFileDialog: () => void;
   getInputProps: () => React.InputHTMLAttributes<HTMLInputElement>;
}

const DEFAULT_OPTIONS: Required<GalleryUploadOptions> = {
   maxFiles: 20,
   maxSizeBytes: 10 * 1024 * 1024, // 10MB
   albumId: null,
   collectionIds: [],
   concurrentUploads: 3,
};

export function useGalleryUpload(
   options: GalleryUploadOptions = {}
): [GalleryUploadState, GalleryUploadActions] {
   const opts = { ...DEFAULT_OPTIONS, ...options };
   const inputRef = useRef<HTMLInputElement>(null);
   const uploadAbortControllers = useRef<Map<string, AbortController>>(
      new Map()
   );

   const [state, setState] = useState<GalleryUploadState>({
      files: [],
      isUploading: false,
      uploadQueue: [],
      activeUploads: new Set(),
      errors: [],
      isDragging: false,
   });

   const uploadImageMutation = useUploadImage();
   const isProcessingQueue = useRef(false);
   const uploadedFiles = useRef(new Set<string>());

   const generateFileId = useCallback((file: File): string => {
      return `${file.name}-${file.size}-${Date.now()}-${Math.random()
         .toString(36)
         .substring(2, 9)}`;
   }, []);

   const createPreview = useCallback((file: File): string => {
      return URL.createObjectURL(file);
   }, []);

   const addFiles = useCallback(
      async (newFiles: FileList | File[]) => {
         const filesArray = Array.from(newFiles);
         const errors: string[] = [];

         // Check max files limit
         if (state.files.length + filesArray.length > opts.maxFiles) {
            errors.push(`Maximum ${opts.maxFiles} files allowed`);
            setState((prev) => ({ ...prev, errors }));
            return;
         }

         const validFiles: FileUploadItem[] = [];

         for (const file of filesArray) {
            // Check for duplicates
            const isDuplicate = state.files.some(
               (existingFile) =>
                  existingFile.file.name === file.name &&
                  existingFile.file.size === file.size
            );

            if (isDuplicate) {
               continue; // Skip duplicates silently
            }

            // Validate file
            const validationErrors = validateImageFile(file, opts.maxSizeBytes);
            if (validationErrors.length > 0) {
               errors.push(...validationErrors);
               continue;
            }

            try {
               // Extract metadata
               const metadata = await extractImageMetadata(file);

               const fileItem: FileUploadItem = {
                  id: generateFileId(file),
                  file,
                  preview: createPreview(file),
                  status: "pending",
                  progress: 0,
                  metadata: {
                     width: metadata.width,
                     height: metadata.height,
                     size: metadata.size,
                     aspectRatio: metadata.aspectRatio,
                  },
               };

               validFiles.push(fileItem);
            } catch (error) {
               errors.push(
                  `Failed to process ${file.name}: ${
                     error instanceof Error ? error.message : "Unknown error"
                  }`
               );
            }
         }

         setState((prev) => ({
            ...prev,
            files: [...prev.files, ...validFiles],
            errors,
         }));
      },
      [
         state.files,
         opts.maxFiles,
         opts.maxSizeBytes,
         generateFileId,
         createPreview,
      ]
   );

   const removeFile = useCallback((id: string) => {
      setState((prev) => {
         const fileToRemove = prev.files.find((f) => f.id === id);
         if (fileToRemove?.preview) {
            URL.revokeObjectURL(fileToRemove.preview);
         }

         // Remove from uploaded files set if it was uploaded
         if (fileToRemove) {
            const fileKey = `${fileToRemove.file.name}-${fileToRemove.file.size}`;
            uploadedFiles.current.delete(fileKey);
         }

         // Cancel upload if in progress
         const controller = uploadAbortControllers.current.get(id);
         if (controller) {
            controller.abort();
            uploadAbortControllers.current.delete(id);
         }

         return {
            ...prev,
            files: prev.files.filter((f) => f.id !== id),
            uploadQueue: prev.uploadQueue.filter((queueId) => queueId !== id),
            activeUploads: new Set(
               [...prev.activeUploads].filter((activeId) => activeId !== id)
            ),
         };
      });
   }, []);

   const clearFiles = useCallback(() => {
      // Clean up previews and abort uploads
      state.files.forEach((file) => {
         if (file.preview) {
            URL.revokeObjectURL(file.preview);
         }
         const controller = uploadAbortControllers.current.get(file.id);
         if (controller) {
            controller.abort();
         }
      });

      uploadAbortControllers.current.clear();
      uploadedFiles.current.clear();

      setState({
         files: [],
         isUploading: false,
         uploadQueue: [],
         activeUploads: new Set(),
         errors: [],
         isDragging: false,
      });
   }, [state.files]);

   const processUploadQueue = useCallback(async () => {
      // Prevent multiple simultaneous queue processing
      if (isProcessingQueue.current) {
         return;
      }

      setState((prev) => {
         const { uploadQueue, activeUploads, files } = prev;

         // Start new uploads up to the concurrent limit
         const availableSlots = opts.concurrentUploads - activeUploads.size;
         const toStart = uploadQueue.slice(0, availableSlots);

         if (toStart.length === 0) {
            return prev;
         }

         const newActiveUploads = new Set([...activeUploads, ...toStart]);
         const newQueue = uploadQueue.slice(availableSlots);

         // Mark as processing to prevent race conditions
         isProcessingQueue.current = true;

         // Process uploads one by one to avoid race conditions
         const processUploads = async () => {
            for (const fileId of toStart) {
               const file = files.find((f) => f.id === fileId);
               if (!file) continue;

               // Check if this file has already been uploaded
               const fileKey = `${file.file.name}-${file.file.size}`;
               if (uploadedFiles.current.has(fileKey)) {
                  // Mark as success since it's already uploaded
                  setState((current) => ({
                     ...current,
                     files: current.files.map((f) =>
                        f.id === fileId
                           ? {
                                ...f,
                                status: "success" as UploadStatus,
                                progress: 100,
                             }
                           : f
                     ),
                     activeUploads: new Set(
                        [...current.activeUploads].filter((id) => id !== fileId)
                     ),
                  }));
                  continue;
               }

               try {
                  // Update status to uploading
                  setState((current) => ({
                     ...current,
                     files: current.files.map((f) =>
                        f.id === fileId
                           ? {
                                ...f,
                                status: "uploading" as UploadStatus,
                                progress: 0,
                             }
                           : f
                     ),
                  }));

                  // Create FormData
                  const formData = new FormData();
                  formData.append("file", file.file);
                  formData.append("albumId", opts.albumId || "");
                  formData.append(
                     "collectionIds",
                     JSON.stringify(opts.collectionIds || [])
                  );
                  formData.append(
                     "width",
                     file.metadata?.width.toString() || "0"
                  );
                  formData.append(
                     "height",
                     file.metadata?.height.toString() || "0"
                  );

                  // Upload the file
                  const result = await uploadImageMutation.mutateAsync(
                     formData
                  );

                  if (result.success) {
                     // Mark file as uploaded to prevent duplicates
                     uploadedFiles.current.add(fileKey);

                     setState((current) => ({
                        ...current,
                        files: current.files.map((f) =>
                           f.id === fileId
                              ? {
                                   ...f,
                                   status: "success" as UploadStatus,
                                   progress: 100,
                                }
                              : f
                        ),
                        activeUploads: new Set(
                           [...current.activeUploads].filter(
                              (id) => id !== fileId
                           )
                        ),
                     }));
                  } else {
                     setState((current) => ({
                        ...current,
                        files: current.files.map((f) =>
                           f.id === fileId
                              ? {
                                   ...f,
                                   status: "error" as UploadStatus,
                                   error: result.error || "Upload failed",
                                }
                              : f
                        ),
                        activeUploads: new Set(
                           [...current.activeUploads].filter(
                              (id) => id !== fileId
                           )
                        ),
                     }));
                  }
               } catch (error) {
                  setState((current) => ({
                     ...current,
                     files: current.files.map((f) =>
                        f.id === fileId
                           ? {
                                ...f,
                                status: "error" as UploadStatus,
                                error:
                                   error instanceof Error
                                      ? error.message
                                      : "Upload failed",
                             }
                           : f
                     ),
                     activeUploads: new Set(
                        [...current.activeUploads].filter((id) => id !== fileId)
                     ),
                  }));
               }
            }

            // Mark processing as complete
            isProcessingQueue.current = false;

            // Check if there are more items in the queue to process
            setState((current) => {
               const hasMoreToProcess =
                  current.uploadQueue.length > 0 &&
                  current.activeUploads.size < opts.concurrentUploads;

               if (hasMoreToProcess) {
                  // Schedule next batch processing
                  setTimeout(() => processUploadQueue(), 100);
               } else if (
                  current.activeUploads.size === 0 &&
                  current.uploadQueue.length === 0
               ) {
                  // All uploads complete
                  return {
                     ...current,
                     isUploading: false,
                  };
               }
               return current;
            });
         };

         // Start processing uploads
         processUploads();

         return {
            ...prev,
            uploadQueue: newQueue,
            activeUploads: newActiveUploads,
            isUploading: newActiveUploads.size > 0 || newQueue.length > 0,
         };
      });
   }, [
      opts.concurrentUploads,
      opts.albumId,
      opts.collectionIds,
      uploadImageMutation,
   ]);

   const uploadFile = useCallback(
      async (id: string) => {
         setState((prev) => {
            const file = prev.files.find((f) => f.id === id);
            if (
               !file ||
               file.status === "uploading" ||
               file.status === "success"
            ) {
               return prev;
            }

            const newQueue = prev.uploadQueue.includes(id)
               ? prev.uploadQueue
               : [...prev.uploadQueue, id];

            return {
               ...prev,
               uploadQueue: newQueue,
               isUploading: true,
            };
         });

         // Process the queue immediately
         processUploadQueue();
      },
      [processUploadQueue]
   );

   const uploadAll = useCallback(async () => {
      const pendingFiles = state.files
         .filter((f) => f.status === "pending" || f.status === "error")
         .map((f) => f.id);

      if (pendingFiles.length === 0) return;

      setState((prev) => ({
         ...prev,
         uploadQueue: [...new Set([...prev.uploadQueue, ...pendingFiles])],
         isUploading: true,
      }));

      // Process the queue immediately
      processUploadQueue();
   }, [state.files, processUploadQueue]);

   const cancelUpload = useCallback((id: string) => {
      const controller = uploadAbortControllers.current.get(id);
      if (controller) {
         controller.abort();
         uploadAbortControllers.current.delete(id);
      }

      setState((prev) => ({
         ...prev,
         files: prev.files.map((f) =>
            f.id === id ? { ...f, status: "cancelled" as UploadStatus } : f
         ),
         uploadQueue: prev.uploadQueue.filter((queueId) => queueId !== id),
         activeUploads: new Set(
            [...prev.activeUploads].filter((activeId) => activeId !== id)
         ),
         isUploading:
            prev.activeUploads.size > 1 || prev.uploadQueue.length > 1,
      }));
   }, []);

   const cancelAllUploads = useCallback(() => {
      // Abort all active uploads
      uploadAbortControllers.current.forEach((controller) =>
         controller.abort()
      );
      uploadAbortControllers.current.clear();

      setState((prev) => ({
         ...prev,
         files: prev.files.map((f) =>
            f.status === "uploading"
               ? { ...f, status: "cancelled" as UploadStatus }
               : f
         ),
         uploadQueue: [],
         activeUploads: new Set(),
         isUploading: false,
      }));
   }, []);

   const retryUpload = useCallback(
      async (id: string) => {
         setState((prev) => ({
            ...prev,
            files: prev.files.map((f) =>
               f.id === id
                  ? {
                       ...f,
                       status: "pending" as UploadStatus,
                       error: undefined,
                    }
                  : f
            ),
         }));

         await uploadFile(id);
      },
      [uploadFile]
   );

   // Drag and drop handlers
   const handleDragEnter = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setState((prev) => ({ ...prev, isDragging: true }));
   }, []);

   const handleDragLeave = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.currentTarget.contains(e.relatedTarget as Node)) {
         return;
      }
      setState((prev) => ({ ...prev, isDragging: false }));
   }, []);

   const handleDragOver = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
   }, []);

   const handleDrop = useCallback(
      async (e: React.DragEvent) => {
         e.preventDefault();
         e.stopPropagation();
         setState((prev) => ({ ...prev, isDragging: false }));

         if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            await addFiles(e.dataTransfer.files);
         }
      },
      [addFiles]
   );

   const handleFileChange = useCallback(
      (e: ChangeEvent<HTMLInputElement>) => {
         if (e.target.files && e.target.files.length > 0) {
            addFiles(e.target.files);
         }
      },
      [addFiles]
   );

   const openFileDialog = useCallback(() => {
      if (inputRef.current) {
         inputRef.current.click();
      }
   }, []);

   const getInputProps = useCallback(
      (props: InputHTMLAttributes<HTMLInputElement> = {}) => {
         return {
            ...props,
            type: "file",
            accept: "image/*",
            multiple: true,
            ref: inputRef,
            onChange: handleFileChange,
            style: { display: "none" },
         };
      },
      [handleFileChange]
   );

   return [
      state,
      {
         addFiles,
         removeFile,
         clearFiles,
         uploadFile,
         uploadAll,
         cancelUpload,
         cancelAllUploads,
         retryUpload,
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         getInputProps,
      },
   ];
}

// Helper function to format bytes to human-readable format
export const formatBytes = (bytes: number, decimals = 2): string => {
   if (bytes === 0) return "0 Bytes";

   const k = 1024;
   const dm = decimals < 0 ? 0 : decimals;
   const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

   const i = Math.floor(Math.log(bytes) / Math.log(k));

   return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + sizes[i];
};
