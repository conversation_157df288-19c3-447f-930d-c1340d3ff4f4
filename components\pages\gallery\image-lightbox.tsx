"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Image as ImageType } from "@/lib/models";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import {
   ChevronLeft,
   ChevronRight,
   Download,
   Grid3X3,
   Loader2,
   Maximize,
   Minimize,
   Pause,
   Play,
   Share2,
   X,
   ZoomIn,
   ZoomOut,
} from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";

interface ImageLightboxProps {
   images: ImageType[];
   currentIndex: number;
   isOpen: boolean;
   onClose: () => void;
   onNext: () => void;
   onPrevious: () => void;
   animationType?: "slide" | "fade";
   showThumbnails?: boolean;
   enableZoom?: boolean;
   enableSlideshow?: boolean;
   enableDownload?: boolean;
   enableShare?: boolean;
   enableFullscreen?: boolean;
   slideshowInterval?: number;
   className?: string;
}

export default function ImageLightbox({
   images,
   currentIndex,
   isOpen,
   onClose,
   onNext,
   onPrevious,
   animationType = "fade",
   showThumbnails = true,
   enableZoom = true,
   enableSlideshow = true,
   enableDownload = true,
   enableShare = true,
   enableFullscreen = true,
   slideshowInterval = 3000,
   className,
}: ImageLightboxProps) {
   const [isLoading, setIsLoading] = useState(true);
   const [zoom, setZoom] = useState(1);
   const [isSlideshow, setIsSlideshow] = useState(false);
   const [isFullscreen, setIsFullscreen] = useState(false);
   const [showThumbnailStrip, setShowThumbnailStrip] = useState(showThumbnails);
   const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
   const [isDragging, setIsDragging] = useState(false);

   const imageRef = useRef<HTMLDivElement>(null);
   const slideshowRef = useRef<NodeJS.Timeout>(null);
   const containerRef = useRef<HTMLDivElement>(null);

   const currentImage = images[currentIndex];

   // Fullscreen API
   const toggleFullscreen = useCallback(async () => {
      if (!containerRef.current) return;

      try {
         if (!document.fullscreenElement) {
            await containerRef.current.requestFullscreen();
            setIsFullscreen(true);
         } else {
            await document.exitFullscreen();
            setIsFullscreen(false);
         }
      } catch (error) {
         console.error("Fullscreen error:", error);
      }
   }, []);

   // Keyboard navigation
   useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
         if (!isOpen) return;

         switch (e.key) {
            case "Escape":
               onClose();
               break;
            case "ArrowLeft":
               onPrevious();
               break;
            case "ArrowRight":
               onNext();
               break;
            case " ":
               e.preventDefault();
               if (enableSlideshow) {
                  setIsSlideshow(!isSlideshow);
               }
               break;
            case "f":
            case "F":
               if (enableFullscreen) {
                  toggleFullscreen();
               }
               break;
            case "+":
            case "=":
               if (enableZoom) {
                  handleZoomIn();
               }
               break;
            case "-":
               if (enableZoom) {
                  handleZoomOut();
               }
               break;
         }
      };

      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
   }, [
      isOpen,
      isSlideshow,
      onClose,
      onNext,
      onPrevious,
      enableSlideshow,
      enableFullscreen,
      enableZoom,
      toggleFullscreen,
   ]);

   // Slideshow functionality
   useEffect(() => {
      if (isSlideshow && isOpen) {
         slideshowRef.current = setInterval(() => {
            onNext();
         }, slideshowInterval);
      } else {
         if (slideshowRef.current) {
            clearInterval(slideshowRef.current);
         }
      }

      return () => {
         if (slideshowRef.current) {
            clearInterval(slideshowRef.current);
         }
      };
   }, [isSlideshow, isOpen, onNext, slideshowInterval]);

   // Reset states when image changes
   useEffect(() => {
      setIsLoading(true);
      setZoom(1);
      setImagePosition({ x: 0, y: 0 });
   }, [currentIndex]);

   // Zoom functionality
   const handleZoomIn = () => {
      setZoom((prev) => Math.min(prev + 0.5, 3));
   };

   const handleZoomOut = () => {
      setZoom((prev) => Math.max(prev - 0.5, 0.5));
   };

   const resetZoom = () => {
      setZoom(1);
      setImagePosition({ x: 0, y: 0 });
   };

   // Download functionality
   const handleDownload = async () => {
      if (!currentImage) return;

      try {
         const response = await fetch(currentImage.url);
         const blob = await response.blob();
         const url = window.URL.createObjectURL(blob);
         const link = document.createElement("a");
         link.href = url;
         link.download = currentImage.name || `image-${currentIndex + 1}`;
         document.body.appendChild(link);
         link.click();
         document.body.removeChild(link);
         window.URL.revokeObjectURL(url);
      } catch (error) {
         console.error("Download failed:", error);
      }
   };

   // Share functionality
   const handleShare = async () => {
      if (!currentImage) return;

      if (navigator.share) {
         try {
            await navigator.share({
               title: currentImage.name || "Image",
               url: currentImage.url,
            });
         } catch (error) {
            console.error("Share failed:", error);
            fallbackShare();
         }
      } else {
         fallbackShare();
      }
   };

   const fallbackShare = () => {
      if (currentImage) {
         navigator.clipboard.writeText(currentImage.url);
         // You could add a toast notification here
      }
   };

   // Mouse drag functionality for zoomed images
   const handleMouseDown = (e: React.MouseEvent) => {
      if (zoom > 1) {
         setIsDragging(true);
         e.preventDefault();
      }
   };

   const handleMouseMove = useCallback(
      (e: MouseEvent) => {
         if (isDragging && zoom > 1) {
            setImagePosition((prev) => ({
               x: prev.x + e.movementX / zoom,
               y: prev.y + e.movementY / zoom,
            }));
         }
      },
      [isDragging, zoom]
   );

   const handleMouseUp = useCallback(() => {
      setIsDragging(false);
   }, []);

   // Mouse event listeners
   useEffect(() => {
      if (isDragging) {
         document.addEventListener("mousemove", handleMouseMove);
         document.addEventListener("mouseup", handleMouseUp);
         return () => {
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
         };
      }
   }, [isDragging, handleMouseMove, handleMouseUp]);

   if (!isOpen || !currentImage) return null;

   const slideVariants = {
      enter: (direction: number) => ({
         x: direction > 0 ? 1000 : -1000,
         opacity: 0,
      }),
      center: {
         zIndex: 1,
         x: 0,
         opacity: 1,
      },
      exit: (direction: number) => ({
         zIndex: 0,
         x: direction < 0 ? 1000 : -1000,
         opacity: 0,
      }),
   };

   const fadeVariants = {
      enter: {
         opacity: 0,
      },
      center: {
         opacity: 1,
      },
      exit: {
         opacity: 0,
      },
   };

   return (
      <AnimatePresence>
         <motion.div
            ref={containerRef}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={cn(
               "fixed inset-0 z-50 bg-black/95 backdrop-blur-sm",
               "flex flex-col",
               className
            )}
            onClick={(e) => {
               if (e.target === e.currentTarget) onClose();
            }}
         >
            {/* Header Controls */}
            <div className="absolute top-0 left-0 right-0 z-10 p-4">
               <div className="flex items-center justify-between">
                  {/* Left Controls */}
                  <div className="flex items-center gap-2">
                     <div className="text-white/80 text-sm">
                        {currentIndex + 1} / {images.length}
                     </div>
                  </div>

                  {/* Right Controls */}
                  <div className="flex items-center gap-2">
                     {enableZoom && (
                        <>
                           <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleZoomOut}
                              className="text-white hover:bg-white/20"
                              disabled={zoom <= 0.5}
                           >
                              <ZoomOut className="w-4 h-4" />
                           </Button>
                           <Button
                              variant="ghost"
                              size="sm"
                              onClick={resetZoom}
                              className="text-white hover:bg-white/20 text-xs px-2"
                           >
                              {Math.round(zoom * 100)}%
                           </Button>
                           <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleZoomIn}
                              className="text-white hover:bg-white/20"
                              disabled={zoom >= 3}
                           >
                              <ZoomIn className="w-4 h-4" />
                           </Button>
                        </>
                     )}

                     {enableSlideshow && (
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={() => setIsSlideshow(!isSlideshow)}
                           className="text-white hover:bg-white/20"
                        >
                           {isSlideshow ? (
                              <Pause className="w-4 h-4" />
                           ) : (
                              <Play className="w-4 h-4" />
                           )}
                        </Button>
                     )}

                     {showThumbnails && (
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={() =>
                              setShowThumbnailStrip(!showThumbnailStrip)
                           }
                           className="text-white hover:bg-white/20"
                        >
                           <Grid3X3 className="w-4 h-4" />
                        </Button>
                     )}

                     {enableFullscreen && (
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={toggleFullscreen}
                           className="text-white hover:bg-white/20"
                        >
                           {isFullscreen ? (
                              <Minimize className="w-4 h-4" />
                           ) : (
                              <Maximize className="w-4 h-4" />
                           )}
                        </Button>
                     )}

                     {enableShare && (
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={handleShare}
                           className="text-white hover:bg-white/20"
                        >
                           <Share2 className="w-4 h-4" />
                        </Button>
                     )}

                     {enableDownload && (
                        <Button
                           variant="ghost"
                           size="sm"
                           onClick={handleDownload}
                           className="text-white hover:bg-white/20"
                        >
                           <Download className="w-4 h-4" />
                        </Button>
                     )}

                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClose}
                        className="text-white hover:bg-white/20"
                     >
                        <X className="w-4 h-4" />
                     </Button>
                  </div>
               </div>
            </div>

            {/* Main Image Area */}
            <div className="flex-1 flex items-center justify-center relative overflow-hidden h-full">
               {/* Navigation Buttons */}
               <Button
                  variant="ghost"
                  size="lg"
                  onClick={onPrevious}
                  className="absolute left-4 z-10 text-white hover:bg-white/20 rounded-full p-3"
                  disabled={images.length <= 1}
               >
                  <ChevronLeft className="w-8 h-8" />
               </Button>

               <Button
                  variant="ghost"
                  size="lg"
                  onClick={onNext}
                  className="absolute right-4 z-10 text-white hover:bg-white/20 rounded-full p-3"
                  disabled={images.length <= 1}
               >
                  <ChevronRight className="w-8 h-8" />
               </Button>

               {/* Image Container */}
               <div
                  ref={imageRef}
                  className={cn(
                     "relative transition-transform duration-200",
                     zoom > 1
                        ? "cursor-grab active:cursor-grabbing"
                        : "cursor-default"
                  )}
                  style={{
                     transform: `scale(${zoom}) translate(${imagePosition.x}px, ${imagePosition.y}px)`,
                  }}
                  onMouseDown={handleMouseDown}
               >
                  <AnimatePresence mode="wait" custom={1}>
                     <motion.div
                        key={currentIndex}
                        custom={1}
                        variants={
                           animationType === "slide"
                              ? slideVariants
                              : fadeVariants
                        }
                        initial="enter"
                        animate="center"
                        exit="exit"
                        transition={{
                           x: { type: "spring", stiffness: 300, damping: 30 },
                           opacity: { duration: 0.2 },
                        }}
                     >
                        {isLoading && (
                           <div className="absolute inset-0 flex items-center justify-center">
                              <Loader2 className="w-8 h-8 text-white animate-spin" />
                           </div>
                        )}

                        <Image
                           src={currentImage.url}
                           alt={
                              currentImage.name || `Image ${currentIndex + 1}`
                           }
                           width={currentImage.width}
                           height={currentImage.height}
                           className="object-contain max-w-[90vw] max-h-[90vh]"
                           onLoad={() => setIsLoading(false)}
                           priority
                        />
                     </motion.div>
                  </AnimatePresence>
               </div>
            </div>

            {/* Thumbnail Strip */}
            {showThumbnailStrip && showThumbnails && images.length > 1 && (
               <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
                  <div className="flex justify-center gap-2 overflow-x-auto max-w-full py-2">
                     {images.map((image, index) => (
                        <button
                           key={image._id}
                           onClick={() => {
                              const direction = index > currentIndex ? 1 : -1;
                              if (index !== currentIndex) {
                                 if (direction > 0) {
                                    for (
                                       let i = 0;
                                       i < Math.abs(index - currentIndex);
                                       i++
                                    ) {
                                       setTimeout(() => onNext(), i * 50);
                                    }
                                 } else {
                                    for (
                                       let i = 0;
                                       i < Math.abs(index - currentIndex);
                                       i++
                                    ) {
                                       setTimeout(() => onPrevious(), i * 50);
                                    }
                                 }
                              }
                           }}
                           className={cn(
                              "relative w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 flex-shrink-0",
                              index === currentIndex
                                 ? "border-white shadow-lg scale-110"
                                 : "border-white/30 hover:border-white/60"
                           )}
                        >
                           <Image
                              src={image.url}
                              alt={image.name || `Thumbnail ${index + 1}`}
                              fill
                              className="object-cover"
                              sizes="64px"
                           />
                        </button>
                     ))}
                  </div>
               </div>
            )}
         </motion.div>
      </AnimatePresence>
   );
}
