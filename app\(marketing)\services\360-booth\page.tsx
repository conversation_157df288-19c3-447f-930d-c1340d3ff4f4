import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import {
   ArrowRight,
   RotateCcw,
   Settings,
   Smartphone,
   Users,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const features = [
   {
      icon: RotateCcw,
      title: "360° Video Experience",
      description:
         "State-of-the-art 360-degree video booth that captures stunning slow-motion videos from every angle.",
   },
   {
      icon: Users,
      title: "Group Friendly",
      description:
         "Large platform accommodates multiple people, making it perfect for group celebrations and parties.",
   },
   {
      icon: Smartphone,
      title: "Instant Sharing",
      description:
         "Videos are processed instantly and can be shared immediately via QR code, email, or social media.",
   },
   {
      icon: Settings,
      title: "Professional Setup",
      description:
         "Our team handles complete setup, operation, and breakdown, ensuring a seamless experience for your event.",
   },
];

const eventTypes = [
   {
      title: "Weddings",
      description:
         "Add excitement to your wedding reception with a 360 video booth that guests will love.",
   },
   {
      title: "Corporate Events",
      description:
         "Perfect for product launches, company parties, and team building events with custom branding.",
   },
   {
      title: "Birthday Parties",
      description:
         "Make any birthday celebration unforgettable with this unique entertainment experience.",
   },
   {
      title: "Graduations",
      description:
         "Celebrate achievements with a fun way for graduates and families to capture memories.",
   },
   {
      title: "Anniversaries",
      description:
         "Create lasting memories for milestone anniversaries with this innovative video experience.",
   },
   {
      title: "Holiday Parties",
      description:
         "Add festive fun to holiday celebrations with themed props and custom branding.",
   },
];

export default function Booth360Page() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/360-photo-booth.jpg"
                  alt="360 video booth rental hero"
                  fill
                  className="object-cover"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                  360 Video Booth Rental
               </h1>
               <p className="text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
                  Add excitement to your event with our state-of-the-art 360
                  video booth that creates stunning slow-motion videos
               </p>
               <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                  <Button asChild size="lg">
                     <Link href="/contact">Book Your 360 Booth</Link>
                  </Button>
                  <Button
                     asChild
                     variant="outline"
                     size="lg"
                     className="text-white hover:bg-white hover:text-black"
                  >
                     <Link href="/portfolio">See It In Action</Link>
                  </Button>
               </div>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Why Choose Our 360 Video Booth
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Our professional-grade 360 video booth provides an
                     unforgettable experience that your guests will be talking
                     about long after your event.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <Card key={index} className="text-center">
                        <CardHeader>
                           <div className="flex justify-center mb-4">
                              <feature.icon className="h-12 w-12 text-primary" />
                           </div>
                           <CardTitle>{feature.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           <CardDescription className="text-center">
                              {feature.description}
                           </CardDescription>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* Event Types Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Perfect for Any Event
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Our 360 video booth is the perfect addition to any
                     celebration, creating memorable experiences for guests of
                     all ages.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {eventTypes.map((event, index) => (
                     <Card key={index}>
                        <CardHeader>
                           <CardTitle>{event.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           <CardDescription>
                              {event.description}
                           </CardDescription>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                     Ready to Add Excitement to Your{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Event?{" "}
                     </span>
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat mb-8 leading-relaxed">
                     Book our 360 video booth and give your guests an
                     unforgettable experience they&apos;ll be sharing for weeks.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button
                        asChild
                        size="lg"
                        className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/contact">
                           Contact Now <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                  </div>
               </div>
            </div>
         </section>
      </div>
   );
}
