import type { NextConfig } from "next";

const nextConfig: NextConfig = {
   images: {
      remotePatterns: [
         {
            protocol: "https",
            hostname: "picsum.photos",
         },
         {
            protocol: "https",
            hostname: "*.r2.cloudflarestorage.com",
         },
         {
            protocol: "https",
            hostname: "pub-257dba4baebe41cfa2bc4fbad3bf847a.r2.dev",
         },
      ],
   },
   experimental: {
      serverActions: {
         bodySizeLimit: "20mb",
      },
   },
};

export default nextConfig;
