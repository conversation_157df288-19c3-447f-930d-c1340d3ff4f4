import { ImageGallery } from "@/components/image-gallery";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Heart, Home, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const galleryImages = [
   {
      src: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
      alt: "Maternity photography session",
   },
   {
      src: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG",
      alt: "Pregnancy portrait outdoors",
   },
   {
      src: "/images/pregnancy-shoots/pregnancy-shoot-3.jpg",
      alt: "Expecting mother photography",
   },
   {
      src: "/images/pregnancy-shoots/pregnancy-shoot-4.jpg",
      alt: "Maternity couple session",
   },
   {
      src: "/images/pregnancy-shoots/pregnancy-shoot-5.jpg",
      alt: "Pregnancy photography studio",
   },
   {
      src: "/images/pregnancy-shoots/pregnancy-shoot-6.JPG",
      alt: "Maternity lifestyle photography",
   },
];

const features = [
   {
      icon: Heart,
      title: "Celebrate This Journey",
      description:
         "Capture the beauty and emotion of pregnancy with elegant, artistic portraits that celebrate this special time.",
   },
   {
      icon: Camera,
      title: "Professional Expertise",
      description:
         "Our experienced photographers specialize in maternity photography, ensuring comfortable and beautiful sessions.",
   },
   {
      icon: Home,
      title: "Flexible Locations",
      description:
         "Choose from our professional studio, outdoor locations, or the comfort of your own home.",
   },
   {
      icon: Users,
      title: "Include Your Family",
      description:
         "Include your partner and children in the session to capture the growing family dynamic.",
   },
];

export default function PregnancyPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/pregnancy-shoots/pregnancy-shoot-1.jpg"
                  alt="Pregnancy photography hero"
                  fill
                  className="object-cover"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                  Pregnancy Photography
               </h1>
               <p className="text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
                  Celebrate this magical time with elegant maternity portraits
                  that capture the beauty and anticipation of pregnancy
               </p>
               <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                  <Button asChild size="lg">
                     <Link href="/contact">Book Your Session</Link>
                  </Button>
                  <Button
                     asChild
                     variant="outline"
                     size="lg"
                     className="text-white hover:bg-white hover:text-black"
                  >
                     <Link href="/portfolio">View Gallery</Link>
                  </Button>
               </div>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Why Choose Our Maternity Photography
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     We specialize in creating beautiful, comfortable maternity
                     photography sessions that celebrate this incredible
                     journey.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <Card key={index} className="text-center">
                        <CardHeader>
                           <div className="flex justify-center mb-4">
                              <feature.icon className="h-12 w-12 text-primary" />
                           </div>
                           <CardTitle>{feature.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           <CardDescription className="text-center">
                              {feature.description}
                           </CardDescription>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Recent Maternity Photography
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Browse through our recent maternity photography sessions to
                     see our style and approach to capturing this special time.
                  </p>
               </div>

               <ImageGallery images={galleryImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                     Ready to Capture This
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Special Time?{" "}
                     </span>
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat mb-8 leading-relaxed">
                     Let&apos;s create beautiful maternity photos that celebrate
                     your pregnancy journey and the anticipation of your new
                     arrival.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button
                        asChild
                        size="lg"
                        className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/contact">
                           Contact Now <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                  </div>
               </div>
            </div>
         </section>
      </div>
   );
}
