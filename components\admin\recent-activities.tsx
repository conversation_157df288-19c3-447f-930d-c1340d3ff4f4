"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useRecentImages } from "@/lib/hooks/use-images";
import { PhotoIcon } from "@heroicons/react/24/outline";
import Image from "next/image";
import Link from "next/link";

export default function RecentActivities() {
   const { data: recentImages, isLoading } = useRecentImages(6);

   if (isLoading) {
      return (
         <Card className="border-border/50">
            <CardHeader>
               <CardTitle className="text-foreground">
                  Recent Activities
               </CardTitle>
            </CardHeader>
            <CardContent>
               <div className="grid grid-cols-2 gap-3">
                  {Array.from({ length: 6 }).map((_, i) => (
                     <div key={i} className="flex gap-3 p-2">
                        <Skeleton className="w-16 h-16 rounded-md flex-shrink-0" />
                        <div className="flex-1 space-y-2">
                           <Skeleton className="h-3 w-1/2" />
                           <Skeleton className="h-3 w-1/3" />
                           <Skeleton className="h-3 w-1/4" />
                        </div>
                     </div>
                  ))}
               </div>
            </CardContent>
         </Card>
      );
   }

   if (!recentImages || recentImages.length === 0) {
      return (
         <Card className="border-border/50">
            <CardHeader>
               <CardTitle className="text-foreground">Recent Uploads</CardTitle>
            </CardHeader>
            <CardContent>
               <div className="flex items-center justify-center h-32 text-muted-foreground">
                  <div className="text-center">
                     <PhotoIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
                     <p className="text-sm">No recent activity</p>
                  </div>
               </div>
            </CardContent>
         </Card>
      );
   }

   return (
      <Card className="border-border/50">
         <CardHeader className="flex items-center justify-between">
            <CardTitle className="text-foreground">Recent Uploads</CardTitle>
            {recentImages.length > 0 && (
               <div className="">
                  <Link
                     href="/admin/ungrouped"
                     className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                     View all images →
                  </Link>
               </div>
            )}
         </CardHeader>
         <CardContent>
            <div className="grid grid-cols-2 gap-3">
               {recentImages.map((image) => (
                  <div
                     key={image._id}
                     className="flex gap-3 p-2 rounded-lg border border-border/50 hover:border-border transition-colors"
                  >
                     <div className="flex-shrink-0">
                        <div className="w-16 h-16 relative overflow-hidden rounded-md">
                           <Image
                              src={image.url}
                              alt={image.name}
                              fill
                              className="object-cover"
                              sizes="64px"
                           />
                        </div>
                     </div>
                     <div className="flex-1 min-w-0 space-y-1">
                        <div className="flex items-start justify-between">
                           <h4 className="text-sm font-medium text-foreground line-clamp-1">
                              {image.name}
                           </h4>
                        </div>
                        <div className="text-xs text-muted-foreground font-medium flex-shrink-0">
                           {new Date(image.createdAt).toLocaleDateString()}
                        </div>

                        {image.albumName && (
                           <div className="text-xs text-muted-foreground line-clamp-1 font-medium">
                              {image.albumName}
                           </div>
                        )}
                     </div>
                  </div>
               ))}
            </div>
         </CardContent>
      </Card>
   );
}
