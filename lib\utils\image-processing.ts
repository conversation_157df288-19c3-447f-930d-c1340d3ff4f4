import { ImageSize, ImageAspectRatio, classifyImageSize, classifyImageAspectRatio } from '@/lib/models/image';

/**
 * Get image dimensions from a File object
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
}

/**
 * Validate if file is a supported image type
 */
export function isValidImageType(file: File): boolean {
  const supportedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ];
  
  return supportedTypes.includes(file.type);
}

/**
 * Generate a unique filename for upload
 */
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = originalName.split('.').pop() || '';
  const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, '');
  
  // Sanitize filename
  const sanitizedName = nameWithoutExtension
    .replace(/[^a-zA-Z0-9-_]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
  
  return `${sanitizedName}-${timestamp}-${randomString}.${extension}`;
}

/**
 * Get optimal grid column span based on image size and aspect ratio
 */
export function getGridColumnSpan(size: ImageSize, aspectRatio: ImageAspectRatio): number {
  // For masonry grid layout
  switch (size) {
    case 'large':
      return aspectRatio === 'wide' ? 2 : 1;
    case 'medium':
      return aspectRatio === 'wide' ? 2 : 1;
    case 'small':
      return 1;
    default:
      return 1;
  }
}

/**
 * Get optimal grid row span based on image size and aspect ratio
 */
export function getGridRowSpan(size: ImageSize, aspectRatio: ImageAspectRatio): number {
  // For masonry grid layout
  switch (size) {
    case 'large':
      return aspectRatio === 'tall' ? 2 : 1;
    case 'medium':
      return aspectRatio === 'tall' ? 2 : 1;
    case 'small':
      return 1;
    default:
      return 1;
  }
}

/**
 * Calculate responsive image sizes string for Next.js Image component
 */
export function getResponsiveImageSizes(size: ImageSize, aspectRatio: ImageAspectRatio): string {
  const baseSize = getGridColumnSpan(size, aspectRatio) > 1 ? '50vw' : '33vw';
  
  return `(max-width: 768px) 100vw, (max-width: 1200px) ${baseSize}, ${baseSize}`;
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Create a thumbnail URL from the main image URL
 * This assumes your CDN/storage supports query parameters for resizing
 */
export function createThumbnailUrl(imageUrl: string, width: number = 300, height: number = 300): string {
  // For Cloudflare R2, you might need to implement image resizing
  // This is a placeholder implementation
  return `${imageUrl}?w=${width}&h=${height}&fit=cover`;
}

/**
 * Extract metadata from image file
 */
export async function extractImageMetadata(file: File) {
  const dimensions = await getImageDimensions(file);
  
  return {
    name: file.name,
    size: classifyImageSize(dimensions.width, dimensions.height),
    aspectRatio: classifyImageAspectRatio(dimensions.width, dimensions.height),
    width: dimensions.width,
    height: dimensions.height,
    fileSize: file.size,
    mimeType: file.type,
    lastModified: new Date(file.lastModified),
  };
}

/**
 * Validate image file before upload
 */
export function validateImageFile(file: File, maxSizeBytes: number = 10 * 1024 * 1024): string[] {
  const errors: string[] = [];
  
  if (!isValidImageType(file)) {
    errors.push('File type not supported. Please upload JPEG, PNG, GIF, WebP, or SVG images.');
  }
  
  if (file.size > maxSizeBytes) {
    errors.push(`File size too large. Maximum size is ${formatFileSize(maxSizeBytes)}.`);
  }
  
  if (file.size === 0) {
    errors.push('File is empty.');
  }
  
  return errors;
}
