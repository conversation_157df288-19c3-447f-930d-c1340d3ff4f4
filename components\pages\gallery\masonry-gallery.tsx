"use client";

import { Image as ImageType } from "@/lib/models";
import { cn } from "@/lib/utils";
import { CloudArrowDownIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { Loader, ZoomIn } from "lucide-react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { Button } from "../../ui/button";
import ImageLightbox from "./image-lightbox";

interface MasonryGalleryProps {
   images: ImageType[];
   columns?: 2 | 3 | 4 | 5;
   gap?: number;
   className?: string;
   enableLightbox?: boolean;
   showLoadingStates?: boolean;
   onImageClick?: (image: ImageType, index: number) => void;
}

export default function MasonryGallery({
   images,
   columns = 3,
   gap = 16,
   className,
   enableLightbox = true,
   showLoadingStates = true,
   onImageClick,
}: MasonryGalleryProps) {
   const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
   const [lightboxOpen, setLightboxOpen] = useState(false);
   const [currentImageIndex, setCurrentImageIndex] = useState(0);
   const containerRef = useRef<HTMLDivElement>(null);

   // Handle image load
   const handleImageLoad = (imageId: string) => {
      setLoadedImages((prev) => new Set([...prev, imageId]));
   };

   // Handle image click
   const handleImageClick = (image: ImageType, index: number) => {
      if (onImageClick) {
         onImageClick(image, index);
      } else if (enableLightbox) {
         setCurrentImageIndex(index);
         setLightboxOpen(true);
      }
   };

   // Handle image download
   const handleDownload = async (image: ImageType, event: React.MouseEvent) => {
      event.stopPropagation(); // Prevent triggering image click

      try {
         const response = await fetch(image.url);
         const blob = await response.blob();
         const url = URL.createObjectURL(blob);

         const link = document.createElement("a");
         link.href = url;
         link.download = image.name || `image_${image._id}.jpg`;
         document.body.appendChild(link);
         link.click();
         document.body.removeChild(link);

         URL.revokeObjectURL(url);
      } catch (error) {
         console.error("Failed to download image:", error);
      }
   };

   // Lightbox navigation
   const handleNext = () => {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
   };

   const handlePrevious = () => {
      setCurrentImageIndex(
         (prev) => (prev - 1 + images.length) % images.length
      );
   };

   // Calculate which column each image should go in
   const getColumnIndex = (index: number, totalColumns: number) => {
      return index % totalColumns;
   };

   const [responsiveColumns, setResponsiveColumns] = useState<number>(columns);

   useEffect(() => {
      const getResponsiveColumns = () => {
         if (typeof window === "undefined") return columns;

         const width = window.innerWidth;
         if (width < 640) return 1;
         if (width < 768) return Math.min(2, columns);
         if (width < 1024) return Math.min(3, columns);
         return columns;
      };

      const handleResize = () => {
         setResponsiveColumns(getResponsiveColumns());
      };

      handleResize();
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
   }, [columns]);

   // Create columns array
   const columnsArray = Array.from(
      { length: responsiveColumns },
      () => [] as ImageType[]
   );

   // Distribute images across columns
   images.forEach((image, index) => {
      const columnIndex = getColumnIndex(index, responsiveColumns);
      if (columnsArray[columnIndex]) {
         columnsArray[columnIndex].push(image);
      }
   });

   const containerVariants = {
      hidden: { opacity: 0 },
      visible: {
         opacity: 1,
         transition: {
            staggerChildren: 0.1,
         },
      },
   };

   const itemVariants = {
      hidden: { opacity: 0, y: 20 },
      visible: {
         opacity: 1,
         y: 0,
         transition: {
            duration: 0.6,
            ease: "easeOut" as const,
         },
      },
   };

   if (images.length === 0) {
      return (
         <div className="flex items-center justify-center h-64 text-muted-foreground">
            <div className="text-center">
               <ZoomIn className="w-12 h-12 mx-auto mb-4 opacity-50" />
               <p className="text-lg font-medium">No images to display</p>
            </div>
         </div>
      );
   }

   return (
      <>
         <motion.div
            ref={containerRef}
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className={cn("w-full", className)}
            style={{ gap }}
         >
            <div className="flex items-start" style={{ gap }}>
               {columnsArray.map((columnImages, columnIndex) => (
                  <div
                     key={columnIndex}
                     className="flex-1 flex flex-col"
                     style={{ gap }}
                  >
                     {columnImages.map((image) => {
                        const globalIndex = images.findIndex(
                           (img) => img._id === image._id
                        );
                        const isLoaded = loadedImages.has(image._id as string);

                        return (
                           <motion.div
                              key={image._id}
                              variants={itemVariants}
                              className="group relative overflow-hidden rounded-lg cursor-pointer"
                              onClick={() =>
                                 handleImageClick(image, globalIndex)
                              }
                           >
                              {/* Loading State */}
                              {showLoadingStates && !isLoaded && (
                                 <div
                                    className="absolute inset-0 bg-astral-grey-light animate-pulse flex items-center justify-center z-10"
                                    style={{
                                       aspectRatio: image.width / image.height,
                                    }}
                                 >
                                    <Loader className="w-6 h-6 animate-spin text-muted-foreground" />
                                 </div>
                              )}

                              {/* Image */}
                              <div className="relative">
                                 <Image
                                    src={image.url}
                                    alt={
                                       image.name || `Image ${globalIndex + 1}`
                                    }
                                    width={image.width}
                                    height={image.height}
                                    className={cn(
                                       "w-full h-auto transition-all duration-500",
                                       // "group-hover:scale-105",
                                       isLoaded ? "opacity-100" : "opacity-0"
                                    )}
                                    onLoad={() =>
                                       handleImageLoad(image._id as string)
                                    }
                                    sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                                 />

                                 {/* Hover Overlay */}
                                 <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center" />
                              </div>

                              {/* Image Info Overlay */}
                              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 sm:opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                 <div className="flex items-center justify-between">
                                    <div className="flex-1 min-w-0">
                                       <p className="text-white text-sm font-medium truncate">
                                          {image.name ||
                                             `Image ${globalIndex + 1}`}
                                       </p>
                                       <p className="text-white/70 text-xs font-medium">
                                          {(
                                             image.fileSize /
                                             1024 /
                                             1024
                                          ).toFixed(2)}{" "}
                                          MB{" "}
                                       </p>
                                    </div>
                                    <Button
                                       onClick={(e) => handleDownload(image, e)}
                                       variant="outline"
                                       className="ml-3 rounded-full size-10 transition-all duration-200 flex-shrink-0"
                                       title="Download image"
                                    >
                                       <CloudArrowDownIcon className="size-6" />
                                    </Button>
                                 </div>
                              </div>
                           </motion.div>
                        );
                     })}
                  </div>
               ))}
            </div>
         </motion.div>

         {/* Lightbox */}
         {enableLightbox && (
            <ImageLightbox
               images={images}
               currentIndex={currentImageIndex}
               isOpen={lightboxOpen}
               onClose={() => setLightboxOpen(false)}
               onNext={handleNext}
               onPrevious={handlePrevious}
               animationType="slide"
               showThumbnails={true}
               enableZoom={true}
               enableSlideshow={true}
               enableDownload={true}
               enableShare={true}
               enableFullscreen={true}
            />
         )}
      </>
   );
}
