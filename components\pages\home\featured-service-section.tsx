"use client";

import { But<PERSON> } from "@/components/ui/button";
import { featuredServices } from "@/lib/data";
import { useScroll } from "framer-motion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { useRef } from "react";
import Card from "./service-list";

export default function FeaturedServiceSection() {
   const container = useRef(null);
   const { scrollYProgress } = useScroll({
      target: container,
      offset: ["start start", "end end"],
   });

   return (
      <>
         <section className="py-20 bg-astral-grey">
            <div className="container max-w-[1400px] mx-auto px-6 lg:px-8 sticky top-0">
               <div className="text-center pb-16">
                  <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                     Featured{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Services
                     </span>
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     From intimate moments to grand celebrations, we offer
                     comprehensive photography and videography services tailored
                     to your unique needs.
                  </p>
               </div>

               <main ref={container} className="relative mt-16">
                  {featuredServices.map((featuredService, i) => {
                     const targetScale =
                        1 - (featuredServices.length - i) * 0.05;
                     return (
                        <Card
                           key={`p_${i}`}
                           i={i}
                           {...featuredService}
                           progress={scrollYProgress}
                           range={[i * 0.25, 1]}
                           targetScale={targetScale}
                        />
                     );
                  })}
               </main>

               <div className="text-center mt-18">
                  <Button asChild size="lg" className="py-4 h-auto !px-6">
                     <Link href="/services">
                        View All Services{" "}
                        <ArrowRight className="ml-2 h-5 w-5" />
                     </Link>
                  </Button>
               </div>
            </div>
         </section>
      </>
   );
}
