"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowRight, ChevronLeft, ChevronRight, X, ZoomIn } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export default function PhotoGallery() {
   const [selectedImage, setSelectedImage] = useState<number | null>(null);

   const galleryImages = [
      {
         src: "/images/wedding-shoots/wedding-shoot-1.JPG",
         alt: "Wedding Photography 1",
         category: "Wedding",
      },
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-1.JPG",
         alt: "Pre-Wedding Photography 1",
         category: "Pre-Wedding",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
         alt: "Pregnancy Photography 1",
         category: "Pregnancy Shoot",
      },
      {
         src: "/images/child-dedication/child-dedication-1.PNG",
         alt: "Child Dedication 1",
         category: "Child Dedication",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-2.PNG",
         alt: "Wedding Photography 2",
         category: "Wedding",
      },
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-2.JPG",
         alt: "Pre-Wedding Photography 2",
         category: "Pre-Wedding",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG",
         alt: "Pregnancy Photography 2",
         category: "Pregnancy Shoot",
      },
      {
         src: "/images/birthday-shoots/birthday-shoot-2.JPG",
         alt: "Birthday Photography 2",
         category: "Birthday Shoot",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-3.JPG",
         alt: "Wedding Photography 3",
         category: "Wedding",
      },
   ];

   const openModal = (index: number) => {
      setSelectedImage(index);
   };

   const closeModal = () => {
      setSelectedImage(null);
   };

   const nextImage = () => {
      if (selectedImage !== null) {
         setSelectedImage((selectedImage + 1) % galleryImages.length);
      }
   };

   const prevImage = () => {
      if (selectedImage !== null) {
         setSelectedImage(
            selectedImage === 0 ? galleryImages.length - 1 : selectedImage - 1
         );
      }
   };

   return (
      <section className="py-20 bg-background">
         <div className="container max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
               className="text-center mb-16"
               initial={{ opacity: 0, y: 30 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.8 }}
               viewport={{ once: true }}
            >
               <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                  Gallery{" "}
                  <span className="bg-gradient-accent bg-clip-text text-transparent">
                     Showcase
                  </span>
               </h2>
               <p className="text-xl text-muted-foreground font-montserrat max-w-3xl mx-auto leading-relaxed">
                  A curated collection of our finest work, showcasing the beauty
                  and emotion we capture
               </p>
            </motion.div>

            {/* Masonry Gallery */}
            <div className="h-screen sm:h-[150vh] gap-y-2 sm:gap-y-6 masonry-container">
               {galleryImages.map((image, index) => (
                  <motion.div
                     key={index}
                     initial={{ opacity: 0, y: 20 }}
                     whileInView={{ opacity: 1, y: 0 }}
                     transition={{ duration: 0.6, delay: index * 0.05 }}
                     viewport={{ once: true }}
                     className="group cursor-pointer break-inside-avoid item bg-astral-grey relative overflow-hidden rounded-sm"
                     onClick={() => openModal(index)}
                  >
                     <Image
                        src={image.src}
                        alt={image.alt}
                        fill
                        className="object-cover transition-all duration-700 group-hover:scale-105 group-hover:brightness-110"
                     />
                     <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                     {/* Hover overlay */}
                     <div className="hidden sm:flex absolute inset-0 items-center justify-center  transition-all duration-300">
                        <motion.div
                           className="w-16 h-16 bg-black/30 opacity-0 group-hover:opacity-100 backdrop-blur-sm rounded-full flex items-center justify-center"
                           whileHover={{ scale: 1.1 }}
                           whileTap={{ scale: 0.9 }}
                        >
                           <ZoomIn className="h-8 w-8 text-white" />
                        </motion.div>
                     </div>

                     {/* Category badge */}
                     <div className="hidden sm:block absolute top-3 left-3 bg-gradient-accent text-white text-xs px-3 py-1 rounded-full font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {image.category}
                     </div>
                  </motion.div>
               ))}
            </div>

            {/* View Portfolio Button */}
            <motion.div
               className="text-center mt-16"
               initial={{ opacity: 0, y: 20 }}
               whileInView={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.6, delay: 0.4 }}
               viewport={{ once: true }}
            >
               <Button
                  asChild
                  size="lg"
                  className="shadow-glow py-4 h-auto !px-6"
               >
                  <Link href="/portfolio">
                     View Full Portfolio
                     <ArrowRight className="h-5 w-5 ml-2" />
                  </Link>
               </Button>
            </motion.div>
         </div>

         {/* Enhanced Modal */}
         <AnimatePresence>
            {selectedImage !== null && (
               <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/85 z-50 flex items-center justify-center p-4"
                  onClick={closeModal}
               >
                  <motion.div
                     className="relative max-w-5xl max-h-full"
                     onClick={(e) => e.stopPropagation()}
                     initial={{ scale: 0.8, rotateY: -15 }}
                     animate={{ scale: 1, rotateY: 0 }}
                     exit={{ scale: 0.8, rotateY: 15 }}
                     transition={{
                        duration: 0.2,
                     }}
                  >
                     <Image
                        src={galleryImages[selectedImage].src}
                        alt={galleryImages[selectedImage].alt}
                        width={900}
                        height={700}
                        className="max-w-full max-h-[90vh] object-contain rounded-xl shadow-2xl"
                     />

                     {/* Enhanced close button */}
                     <motion.button
                        onClick={closeModal}
                        className="absolute top-2 right-2 w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center text-white shadow-glow"
                        whileHover={{ scale: 1.1, rotate: 90 }}
                        whileTap={{ scale: 0.9 }}
                     >
                        <X className="h-6 w-6" />
                     </motion.button>

                     {/* Enhanced navigation */}
                     <motion.button
                        onClick={prevImage}
                        className="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/20 transition-colors"
                        whileHover={{ scale: 1.1, x: -5 }}
                        whileTap={{ scale: 0.9 }}
                     >
                        <ChevronLeft className="h-6 w-6" />
                     </motion.button>
                     <motion.button
                        onClick={nextImage}
                        className="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/20 transition-colors"
                        whileHover={{ scale: 1.1, x: 5 }}
                        whileTap={{ scale: 0.9 }}
                     >
                        <ChevronRight className="h-6 w-6" />
                     </motion.button>

                     {/* Enhanced image info */}
                     <motion.div
                        className="absolute bottom-6 left-1/2 -translate-x-1/2 bg-black/50 backdrop-blur-sm text-white px-6 py-3 rounded-full"
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.2 }}
                     >
                        <div className="text-center">
                           <div className="text-xs opacity-80">
                              {selectedImage + 1} of {galleryImages.length}
                           </div>
                        </div>
                     </motion.div>
                  </motion.div>
               </motion.div>
            )}
         </AnimatePresence>
      </section>
   );
}
