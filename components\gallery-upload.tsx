"use client";

import {
   Al<PERSON><PERSON><PERSON>cleIcon,
   Loader,
   MoreVertical,
   RefreshCw,
   Trash2,
   UploadIcon,
   X,
   XIcon,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
   formatBytes,
   useGalleryUpload,
   type GalleryUploadOptions,
} from "@/hooks/use-gallery-upload";
import { formatFileSize } from "@/lib/utils/image-processing";
import {
   CheckCircleIcon,
   CloudArrowUpIcon,
   PhotoIcon,
} from "@heroicons/react/24/solid";
import Image from "next/image";
import { useEffect } from "react";

interface GalleryUploadProps {
   options?: GalleryUploadOptions;
   className?: string;
   onUploadComplete?: () => void;
   title?: string;
   subtitle?: string;
}

export default function GalleryUpload({
   options,
   className,
   onUploadComplete,
   title,
   subtitle,
}: GalleryUploadProps) {
   const [
      { files, isUploading, errors, isDragging },
      {
         removeFile,
         clearFiles,
         uploadFile,
         uploadAll,
         cancelUpload,
         cancelAllUploads,
         retryUpload,
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         getInputProps,
      },
   ] = useGalleryUpload(options);

   const pendingFiles = files.filter(
      (f) => f.status === "pending" || f.status === "error"
   );
   const successFiles = files.filter((f) => f.status === "success");

   // Call onUploadComplete when all files are uploaded successfully
   useEffect(() => {
      if (
         onUploadComplete &&
         files.length > 0 &&
         files.every((f) => f.status === "success")
      ) {
         onUploadComplete();
      }
   }, [files, onUploadComplete]);

   const getStatusIcon = (status: string) => {
      switch (status) {
         case "uploading":
            return <Loader className="size-6 animate-spin text-blue-500" />;
         case "success":
            return <CheckCircleIcon className="size-6 text-green-500" />;
         case "error":
            return <AlertCircleIcon className="size-6 text-red-500" />;
         case "cancelled":
            return <X className="size-6 text-gray-500" />;
         default:
            return <CloudArrowUpIcon className="size-6 text-gray-700" />;
      }
   };

   return (
      <div className={`flex flex-col gap-4 w-full ${className}`}>
         {/* Drop area */}
         <div
            onClick={files.length > 0 ? undefined : openFileDialog}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            data-dragging={isDragging || undefined}
            data-files={files.length > 0 || undefined}
            className={`border-border/55 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 relative flex min-h-62 flex-col items-center overflow-hidden rounded-xl border-2 border-dashed p-4 transition-colors not-data-[files]:justify-center has-[input:focus]:ring-[3px] hover:bg-astral-grey/50 ${
               files.length > 0 ? "cursor-default" : "cursor-pointer"
            }`}
         >
            <input
               {...getInputProps()}
               className="sr-only"
               aria-label="Upload image file"
            />

            {files.length === 0 ? (
               <>
                  <PhotoIcon className="size-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                     {title || "Upload Images to Gallery"}
                  </h3>
                  <p className="text-sm text-muted-foreground text-center mb-4">
                     {subtitle ||
                        "Drag and drop your images here, or click to browse"}
                  </p>
                  <p className="text-xs text-muted-foreground text-center">
                     Supports JPEG, PNG, GIF, WebP, and SVG files up to{" "}
                     {formatFileSize(options?.maxSizeBytes || 20 * 1024 * 1024)}
                  </p>
               </>
            ) : (
               <div className="w-full">
                  {/* Upload controls */}
                  <div className="flex items-center justify-between mb-4 gap-2">
                     <div>
                        <h3 className="truncate text-sm font-medium">
                           Selected Files ({files.length})
                        </h3>
                        <span className="text-sm text-muted-foreground">
                           {successFiles.length}/{files.length} uploaded
                        </span>
                     </div>
                     {/* Desktop actions */}
                     <div className="hidden sm:flex items-center gap-3 overflow-x-auto">
                        {pendingFiles.length > 0 && (
                           <Button
                              onClick={uploadAll}
                              size="sm"
                              disabled={isUploading}
                              className="rounded-lg py-2 !px-4 h-auto"
                           >
                              <UploadIcon
                                 className="-ms-0.5 mr-1 size-4"
                                 aria-hidden="true"
                              />
                              Upload All
                           </Button>
                        )}
                        {isUploading && (
                           <Button
                              onClick={cancelAllUploads}
                              variant="outline"
                              size="sm"
                              className="rounded-lg py-2 !px-4 h-auto"
                           >
                              <X className="-ms-0.5 mr-1 size-4 opacity-80" />
                              Cancel Uploads
                           </Button>
                        )}
                        <Button
                           variant="outline"
                           size="sm"
                           onClick={openFileDialog}
                           disabled={isUploading}
                           className="rounded-lg py-2 !px-4 h-auto"
                        >
                           <PhotoIcon
                              className="-ms-0.5 mr-1 size-4 opacity-80"
                              aria-hidden="true"
                           />
                           Add more
                        </Button>
                        <Button
                           variant="outline"
                           size="sm"
                           onClick={clearFiles}
                           disabled={isUploading}
                           className="rounded-lg py-2 !px-4 h-auto"
                        >
                           <Trash2
                              className="-ms-0.5 mr-1 size-4 opacity-80"
                              aria-hidden="true"
                           />
                           Remove all
                        </Button>
                     </div>
                     {/* Mobile actions dropdown */}
                     <div className="flex sm:hidden items-center">
                        <DropdownMenu>
                           <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="icon">
                                 <MoreVertical className="w-5 h-5" />
                                 <span className="sr-only">Open actions</span>
                              </Button>
                           </DropdownMenuTrigger>
                           <DropdownMenuContent align="end">
                              {pendingFiles.length > 0 && (
                                 <DropdownMenuItem
                                    onClick={uploadAll}
                                    disabled={isUploading}
                                 >
                                    <UploadIcon className="w-4 h-4 mr-2" />{" "}
                                    Upload All
                                 </DropdownMenuItem>
                              )}
                              {isUploading && (
                                 <DropdownMenuItem onClick={cancelAllUploads}>
                                    <X className="w-4 h-4 mr-2" /> Cancel
                                    Uploads
                                 </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                 onClick={openFileDialog}
                                 disabled={isUploading}
                              >
                                 <PhotoIcon className="w-4 h-4 mr-2" /> Add more
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                 onClick={clearFiles}
                                 disabled={isUploading}
                              >
                                 <Trash2 className="w-4 h-4 mr-2" /> Remove all
                              </DropdownMenuItem>
                           </DropdownMenuContent>
                        </DropdownMenu>
                     </div>
                  </div>

                  {/* Files grid */}
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                     {files.map((fileItem) => (
                        <div
                           className="flex flex-col aspect-square w-full"
                           key={fileItem.id}
                        >
                           <div className="bg-astral-grey/50 relative w-full h-[calc(100%-62.5px)] group/image rounded-md rounded-b-none">
                              <Image
                                 src={fileItem.preview || ""}
                                 alt={fileItem.file.name}
                                 fill
                                 className="size-full rounded-[inherit] object-cover"
                              />

                              {/* Status overlay */}
                              <div className="absolute top-2 left-2">
                                 {getStatusIcon(fileItem.status)}
                              </div>

                              <Button
                                 onClick={() => removeFile(fileItem.id)}
                                 size="icon"
                                 className="border-background focus-visible:border-background absolute -top-2 -right-2 size-6 rounded-full border-2 shadow-none"
                                 aria-label="Remove image"
                              >
                                 <XIcon className="size-3.5" />
                              </Button>

                              <div className="hidden group-hover/image:flex justify-center items-center justify-self-center h-full gap-1 absolute inset-0 size-6 rounded-full">
                                 {fileItem.status === "pending" && (
                                    <Button
                                       onClick={() => uploadFile(fileItem.id)}
                                       size="sm"
                                       variant="outline"
                                       className="flex-1 h-10 text-xs border-0 rounded-full !px-4"
                                    >
                                       <UploadIcon className="size-3.5 mr-1" />
                                       Upload
                                    </Button>
                                 )}

                                 {fileItem.status === "error" && (
                                    <Button
                                       onClick={() => retryUpload(fileItem.id)}
                                       size="sm"
                                       variant="outline"
                                       className="flex-1 h-10 text-xs border-0 rounded-full !px-4"
                                    >
                                       <RefreshCw className="size-3 mr-1" />
                                       Retry
                                    </Button>
                                 )}

                                 {fileItem.status === "uploading" && (
                                    <Button
                                       onClick={() => cancelUpload(fileItem.id)}
                                       size="sm"
                                       variant="outline"
                                       className="flex-1 h-10 text-xs border-0 rounded-full !px-4"
                                    >
                                       <X className="size-3 mr-1" />
                                       Cancel
                                    </Button>
                                 )}
                              </div>
                           </div>
                           <div className="flex min-w-0 flex-col gap-0.5 rounded-b-md p-3 bg-astral-grey/80">
                              <p className="truncate text-[13px] font-medium">
                                 {fileItem.file.name}
                              </p>
                              <p className="text-muted-foreground truncate text-xs">
                                 {formatBytes(fileItem.file.size)}
                                 {fileItem.metadata && (
                                    <span className="ml-1">
                                       • {fileItem.metadata.width}×
                                       {fileItem.metadata.height}
                                    </span>
                                 )}
                              </p>
                           </div>
                        </div>
                     ))}
                  </div>
               </div>
            )}
         </div>

         {/* Error messages */}
         {errors.length > 0 && (
            <div className="rounded-lg border border-red-200 bg-red-50 p-4">
               <div className="flex items-start">
                  <AlertCircleIcon className="size-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="flex-1">
                     <h4 className="text-sm font-medium text-red-800 mb-1">
                        Upload Errors
                     </h4>
                     <ul className="text-sm text-red-700 space-y-1">
                        {errors.map((error, index) => (
                           <li key={index}>• {error}</li>
                        ))}
                     </ul>
                  </div>
               </div>
            </div>
         )}
      </div>
   );
}
