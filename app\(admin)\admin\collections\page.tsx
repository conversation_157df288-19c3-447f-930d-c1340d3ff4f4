"use client";

import CreateCollectionDialog from "@/components/admin/create-collection-dialog";
import {
   AlertDialog,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import {
   useCollections,
   useDeleteCollection,
} from "@/lib/hooks/use-collections";
import { TagIcon } from "@heroicons/react/24/solid";
import { Loader2, MoreVertical, Trash2 } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function CollectionsPage() {
   const { data: collectionsData, isLoading, error } = useCollections();
   const deleteCollectionMutation = useDeleteCollection();
   const [deleteDialog, setDeleteDialog] = useState({
      open: false,
      collectionId: "",
      collectionName: "",
   });
   const [dropdownOpen, setDropdownOpen] = useState<{
      [collectionId: string]: boolean;
   }>({});

   useEffect(() => {
      if (deleteCollectionMutation.isSuccess && deleteDialog.open) {
         setDeleteDialog({ open: false, collectionId: "", collectionName: "" });
         deleteCollectionMutation.reset();
      }
   }, [
      deleteCollectionMutation.isSuccess,
      deleteDialog.open,
      deleteCollectionMutation,
   ]);

   if (error) {
      return (
         <div className="p-8">
            <div className="text-center text-destructive">
               <p>Failed to load collections. Please try again.</p>
            </div>
         </div>
      );
   }

   const collections = collectionsData?.data || [];
   const hasCollections = collections.length > 0;
   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-3xl font-bold text-foreground">
                     Collections
                  </h1>
                  <p className="text-muted-foreground">
                     Manage tags and collections for your images
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-3">
               <CreateCollectionDialog showTrigger={true} />
            </div>
         </div>

         {/* Main Content */}
         {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
               {Array.from({ length: 8 }).map((_, i) => (
                  <Card key={i} className="border-border/50 py-0">
                     <CardContent className="p-6">
                        <div className="flex items-center space-x-3 mb-4">
                           <Skeleton className="w-8 h-8 rounded-full" />
                           <Skeleton className="h-5 w-32" />
                        </div>
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-4 w-3/4" />
                     </CardContent>
                  </Card>
               ))}
            </div>
         ) : hasCollections ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
               {collections.map((collection) => (
                  <div
                     key={collection._id?.toString()}
                     className="relative group"
                  >
                     {/* Dropdown menu (not inside the link) */}
                     <div
                        className="absolute top-2 right-2 group-hover:opacity-100 transition-opacity z-10"
                        onClick={(e) => e.stopPropagation()}
                     >
                        <DropdownMenu
                           open={
                              dropdownOpen[collection._id?.toString() || ""] ||
                              false
                           }
                           onOpenChange={(open) =>
                              setDropdownOpen((prev) => ({
                                 ...prev,
                                 [collection._id?.toString() || ""]: open,
                              }))
                           }
                        >
                           <DropdownMenuTrigger asChild>
                              <Button
                                 variant="outline"
                                 size="sm"
                                 className="h-8 w-8 p-0 bg-background/80 border-none rounded-lg backdrop-blur-sm"
                                 onClick={(e) => e.stopPropagation()}
                              >
                                 <MoreVertical className="w-4 h-4" />
                              </Button>
                           </DropdownMenuTrigger>
                           <DropdownMenuContent
                              align="end"
                              onClick={(e) => e.stopPropagation()}
                           >
                              <DropdownMenuItem
                                 className="group/delete-btn"
                                 onClick={() => {
                                    setDropdownOpen((prev) => ({
                                       ...prev,
                                       [collection._id?.toString() || ""]:
                                          false,
                                    }));
                                    setDeleteDialog({
                                       open: true,
                                       collectionId:
                                          collection._id?.toString() || "",
                                       collectionName: collection.name,
                                    });
                                 }}
                              >
                                 <Trash2 className="w-4 h-4 mr-2 text-destructive group-hover/delete-btn:text-white transition-colors" />
                                 Delete Collection
                              </DropdownMenuItem>
                           </DropdownMenuContent>
                        </DropdownMenu>
                     </div>
                     {/* Make the card content a link */}
                     <Link
                        href={`/admin/collections/${collection._id}`}
                        className="block group/card focus:outline-none focus:ring-primary rounded-lg"
                     >
                        <Card className="border-border/50 group hover:shadow-lg transition-shadow py-0">
                           <CardContent className="p-6 cursor-pointer">
                              <div className="flex items-start mb-4">
                                 <div className="flex items-center space-x-3 flex-1">
                                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                                       <TagIcon className="w-4 h-4 text-primary" />
                                    </div>
                                    <div>
                                       <h3 className="font-semibold text-foreground hover:text-primary transition-colors">
                                          {collection.name}
                                       </h3>
                                    </div>
                                 </div>
                              </div>
                              {collection.description && (
                                 <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                                    {collection.description}
                                 </p>
                              )}
                              <div className="flex items-center justify-between text-sm text-muted-foreground">
                                 <span>{collection.imageCount} images</span>
                                 <span>
                                    Created{" "}
                                    {new Date(
                                       collection.createdAt
                                    ).toLocaleDateString()}
                                 </span>
                              </div>
                           </CardContent>
                        </Card>
                     </Link>
                  </div>
               ))}
            </div>
         ) : (
            <Card className="border-border/50">
               <CardHeader>
                  <CardTitle className="text-foreground">
                     Collection Management
                  </CardTitle>
                  <CardDescription>
                     Create and manage collections to categorize your images
                     across albums
                  </CardDescription>
               </CardHeader>
               <CardContent>
                  <div className="flex items-center justify-center h-64 text-muted-foreground">
                     <div className="text-center">
                        <TagIcon className="w-16 h-16 mx-auto mb-4 opacity-50" />
                        <h3 className="text-lg font-medium mb-2">
                           No collections yet
                        </h3>
                        <p className="text-sm mb-4">
                           Create collections to tag and categorize your images
                        </p>
                        <CreateCollectionDialog showTrigger={true} />
                     </div>
                  </div>
               </CardContent>
            </Card>
         )}
         {/* Delete Collection Dialog */}
         <AlertDialog
            open={deleteDialog.open}
            onOpenChange={(open) => {
               if (!open) {
                  setDeleteDialog({
                     open: false,
                     collectionId: "",
                     collectionName: "",
                  });
                  deleteCollectionMutation.reset();
               } else {
                  setDeleteDialog((prev) => ({ ...prev, open: true }));
               }
            }}
         >
            <AlertDialogContent>
               <AlertDialogHeader>
                  <AlertDialogTitle>Delete Collection</AlertDialogTitle>
                  <AlertDialogDescription className="space-y-1" asChild>
                     <div>
                        <p>
                           Are you sure you want to delete the collection{" "}
                           <span className="font-semibold">
                              {deleteDialog.collectionName}
                           </span>
                           ?
                        </p>
                        <p>
                           This will remove the collection from all images that
                           belong to it.
                        </p>
                        <p>This action cannot be undone.</p>
                     </div>
                  </AlertDialogDescription>
               </AlertDialogHeader>
               <AlertDialogFooter>
                  <AlertDialogCancel
                     onClick={() => {
                        setDeleteDialog({
                           open: false,
                           collectionId: "",
                           collectionName: "",
                        });
                        deleteCollectionMutation.reset();
                     }}
                     disabled={deleteCollectionMutation.isPending}
                  >
                     Cancel
                  </AlertDialogCancel>
                  <button
                     type="button"
                     className="bg-destructive text-sm text-white rounded-md px-4 py-2 font-medium hover:bg-destructive/90 disabled:opacity-50"
                     disabled={deleteCollectionMutation.isPending}
                     onClick={() => {
                        if (!deleteCollectionMutation.isPending) {
                           deleteCollectionMutation.mutate(
                              deleteDialog.collectionId
                           );
                        }
                     }}
                  >
                     {deleteCollectionMutation.isPending ? (
                        <>
                           <Loader2 className="w-4 h-4 animate-spin inline-block mr-2" />
                           Deleting...
                        </>
                     ) : (
                        "Delete"
                     )}
                  </button>
               </AlertDialogFooter>
            </AlertDialogContent>
         </AlertDialog>
      </div>
   );
}
