import { Footer } from "@/components/footer";
import { Navigation } from "@/components/navigation";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: {
      default: "Astral Studios - Professional Photography & Videography",
      template: "%s | Astral Studios",
   },
   description:
      "Professional photography and videography services in the UK. Specializing in weddings, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth rental, and dry ice machine rental.",
   keywords:
      "photography, videography, wedding photography, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth, dry ice machine rental, UK photography studio",
   authors: [{ name: "Astral Studios" }],
   creator: "Astral Studios",
   publisher: "Astral Studios",
   metadataBase: new URL("http://astralstudios.co.uk"),
   alternates: {
      canonical: "http://astralstudios.co.uk",
   },
   robots: {
      index: true,
      follow: true,
      googleBot: {
         index: true,
         follow: true,
         "max-video-preview": -1,
         "max-image-preview": "large",
         "max-snippet": -1,
      },
   },
   openGraph: {
      title: "Astral Studios - Professional Photography & Videography",
      description:
         "Professional photography and videography services specializing in weddings, pre-wedding shoots, pregnancy photography, and more.",
      url: "http://astralstudios.co.uk",
      siteName: "Astral Studios",
      type: "website",
      images: [
         {
            url: "/images/hero.JPG",
            width: 1200,
            height: 630,
            alt: "Astral Studios - Professional Photography",
         },
      ],
   },
   twitter: {
      card: "summary_large_image",
      title: "Astral Studios - Professional Photography & Videography",
      description:
         "Professional photography and videography services in the UK",
      images: ["/images/hero.JPG"],
   },
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <div>
         <Navigation />
         <main className="min-h-screen">{children}</main>
         <Footer />
      </div>
   );
}
